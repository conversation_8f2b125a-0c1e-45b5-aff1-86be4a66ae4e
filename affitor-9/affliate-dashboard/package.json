{"name": "social-listening", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/runtime": "^7.26.9", "@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/language": "^6.11.2", "@codemirror/lint": "^6.8.5", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.38.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@mui/system": "^6.4.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.6.1", "@strapi/blocks-react-renderer": "^1.0.2", "@tanstack/react-table": "^8.21.2", "@types/lodash": "^4.17.16", "@types/react-redux": "^7.1.34", "@types/react-simple-maps": "^3.0.6", "@yoopta/action-menu-list": "^4.9.9", "@yoopta/blockquote": "^4.9.9", "@yoopta/callout": "^4.9.9", "@yoopta/code": "^4.9.9", "@yoopta/divider": "^4.9.9", "@yoopta/editor": "^4.9.9", "@yoopta/embed": "^4.9.9", "@yoopta/headings": "^4.9.9", "@yoopta/image": "^4.9.9", "@yoopta/link": "^4.9.9", "@yoopta/link-tool": "^4.9.9", "@yoopta/lists": "^4.9.9", "@yoopta/marks": "^4.9.9", "@yoopta/paragraph": "^4.9.9", "@yoopta/table": "^4.9.9", "@yoopta/toolbar": "^4.9.9", "@yoopta/video": "^4.9.9", "add": "^2.0.6", "axios": "^1.8.2", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "firebase": "^11.5.0", "form-data": "^4.0.4", "formidable": "^3.5.4", "framer-motion": "^12.10.1", "he": "^1.2.0", "highcharts": "^12.1.2", "highcharts-react-official": "^3.2.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.485.0", "mui-daterange-picker": "^1.0.5", "next": "15.2.0", "next-redux-wrapper": "^8.1.0", "next-themes": "^0.4.6", "nextjs-tui-date-picker": "^2.2.3", "nextjs-tui-date-range-picker": "^2.1.1", "qs": "^6.14.0", "react": "18.2.0", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.4.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-simple-maps": "^3.0.0", "redux": "^5.0.1", "redux-saga": "^1.3.0", "reselect": "^5.1.1", "slate": "^0.117.2", "slate-dom": "^0.117.4", "slate-react": "^0.117.4", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/formidable": "^3.4.5", "@types/he": "^1", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/qs": "^6.9.18", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}