import { NextApiRequest } from "next";

export interface ApiMiddlewareOptions {
  requireAuth?: boolean;
  forwardCookies?: boolean;
}

export interface ApiContext {
  token?: string;
  cookies?: string;
  headers: Record<string, string>;
}

/**
 * Centralized middleware to extract and prepare API context
 */
export const createApiContext = (
  req: NextApiRequest,
  options: ApiMiddlewareOptions = {}
): ApiContext => {
  const { requireAuth = false, forwardCookies = true } = options;

  // Extract token
  const authHeader = req.headers.authorization;
  const token = authHeader?.startsWith("Bearer ")
    ? authHeader.split(" ")[1]
    : undefined;

  // Extract cookies
  const cookies = forwardCookies ? req.headers.cookie : undefined;

  // Create headers object
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  if (cookies) {
    headers.Cookie = cookies;
  }

  // Validation
  if (requireAuth && !token) {
    throw new Error("Authentication required");
  }

  return { token, cookies, headers };
};

/**
 * Higher-order function to wrap API handlers with automatic context
 */
export const withApiContext = (
  handler: (req: NextApiRequest, context: ApiContext) => Promise<any>,
  options: ApiMiddlewareOptions = {}
) => {
  return async (req: NextApiRequest, res: any) => {
    try {
      const context = createApiContext(req, options);
      return await handler(req, context);
    } catch (error: any) {
      if (error.message === "Authentication required") {
        return res.status(401).json({
          statusCode: 401,
          message: "Authentication required",
        });
      }
      throw error;
    }
  };
};
