import { useEffect, useState, useRef } from "react";
import { CustomButton } from "../../components/CustomButton";
import { Skeleton } from "@/components/ui/skeleton";
import { ITag } from "@/interfaces";
import { useDispatch, useSelector } from "react-redux";
import { selectActiveTag, actions as filterActions } from "@/features/filter/filter.slice";
import { affiliateActions } from "@/features/rootActions";
import CustomizeColumnsButton from "./CustomizeColumnsButton";
import { Badge } from "@/components/ui/badge";

interface FilterTagsBarProps {
  initialTagId?: string;
}

export default function FilterTagsBar({ initialTagId = "" }: FilterTagsBarProps) {
  const dispatch = useDispatch();
  const activeTag = useSelector(selectActiveTag);
  
  const [tags, setTags] = useState<ITag[]>([
    {
      id: "",
      name: "All",
      icon: null,
    },
  ]);
  
  const [isLoading, setIsLoading] = useState(false);
  const hasFetchedTags = useRef(false);
  
  // Fetch tags only once and handle initial tag selection
  useEffect(() => {
    if (hasFetchedTags.current) return;
    
    const fetchTags = async () => {
      setIsLoading(true);
      try {
        const res = await fetch("/api/affiliate-tags");
        if (res.status !== 200) {
          throw new Error("Failed to fetch tags");
        }
        const data = await res.json();
        setTags(data);
        
        // If we have an initialTagId, set the active tag
        if (initialTagId) {
          const matchingTag = data.find((tag: ITag) => tag.id === initialTagId);
          if (matchingTag) {
            dispatch(filterActions.setActiveTag(matchingTag));
            
            // Signal that we need to reload affiliate data
            dispatch(affiliateActions.setAffiliates(null));
          }
        }
        
        hasFetchedTags.current = true;
      } catch (error) {
        console.error(error);
        setTags([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
  }, [initialTagId, dispatch]);
  
  const handleTagClick = (tag: ITag) => {
    // If clicking the "All" tag or the already active tag, deselect
    if (tag.id === "" || (activeTag && tag.id === activeTag.id)) {
      dispatch(filterActions.setActiveTag(null));
    } else {
      dispatch(filterActions.setActiveTag(tag));
    }
    
    // Signal that we need to reload affiliate data
    dispatch(affiliateActions.setAffiliates(null));
  };
  
  return (
    <div
      className={`bg-background p-4 pl-3 text-[12px] h-full flex items-center justify-between overflow-scroll hide-scrollbar`}
    >
      <div className="flex space-x-2">
      {isLoading ? (
        <div className="flex space-x-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton 
              key={`skeleton-${i}`} 
              className="h-8 w-24 rounded-md bg-primary-foreground/10" 
            />
          ))}
        </div>
      ) : tags.length === 0 ? (
        <div className="px-4 py-2 text-sm text-gray-500">No tags available</div>
      ) : (
        tags.map((tag, i) => (
            <Badge
            id={`tag-${tag.name}`}
            key={"category_" + i}
            onClick={() => handleTagClick(tag)}
              className={`cursor-pointer transition-all duration-200 px-4 py-2 rounded-full shadow-sm text-[14px] font-semibold flex items-center gap-2
                ${activeTag && activeTag.id === tag.id
                  ? "bg-blue-600 text-white hover:bg-blue-700 dark:text-white"
                  : "bg-white dark:bg-input/30 text-blue-700 dark:text-white border border-blue-100 dark:border-border hover:bg-blue-50 dark:hover:bg-input/50 hover:scale-105"}
              `}
              style={{ boxShadow: activeTag && activeTag.id === tag.id ? "0 2px 8px 0 rgba(56,97,251,0.10)" : "0 1px 4px 0 rgba(56,97,251,0.04)" }}
            >
              {tag.icon && (
                <img
                  src={typeof tag.icon === "object" && tag.icon !== null ? tag.icon.url : "https://via.placeholder.com/150"}
                  width={15}
                  height={15}
                  alt={tag.name}
                  className="inline-block align-middle"
                />
              )}
              <span>{tag.name}</span>
            </Badge>
        ))
      )}
      </div>
      <div className="ml-4 flex-shrink-0">
        <CustomizeColumnsButton />
      </div>
    </div>
  );
}
