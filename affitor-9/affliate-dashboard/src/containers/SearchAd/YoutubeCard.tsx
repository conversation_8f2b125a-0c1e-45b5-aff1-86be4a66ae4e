import React from "react";
import {
  Play,
  Clock,
  ExternalLink,
  Copy,
  Target,
  Zap,
  Globe,
  Users,
  Calendar,
  Eye,
  DollarSign,
  TrendingUp,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { SpyHeroAd } from "@/features/spyhero/spyhero.slice";

interface YoutubeCardProps {
  ad: SpyHeroAd;
  formatNumber: (num: string | number | undefined) => string;
  formatDate: (dateString: string) => string;
  copyToClipboard: (text: string) => void;
}

const YoutubeCard: React.FC<YoutubeCardProps> = ({
  ad,
  formatNumber,
  formatDate,
  copyToClipboard,
}) => {
  return (
    <Card className="group relative overflow-hidden transition-all duration-300 hover:shadow-2xl sm:hover:-translate-y-2 bg-card border-border/50 hover:border-primary/20">
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      <CardHeader className="relative pb-3 sm:pb-4 p-3 sm:p-6">
        {/* Platform badge */}
        <div className="absolute top-3 right-3 sm:top-4 sm:right-4 z-10">
          <Badge className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0 shadow-lg transition-all duration-300 group-hover:scale-110 text-xs sm:text-sm px-2 py-1">
            {ad.network}
          </Badge>
        </div>

        <div className="flex items-start justify-between pr-12 sm:pr-16">
          <div className="flex flex-wrap gap-1 sm:gap-2">
            {ad.niche && (
              <Badge
                variant="outline"
                className="text-xs bg-background/80 backdrop-blur-sm border-primary/20 text-foreground px-2 py-0.5"
              >
                <Target className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                <span className="hidden sm:inline">{ad.niche}</span>
                <span className="sm:hidden">
                  {ad.niche.slice(0, 6)}
                  {ad.niche.length > 6 ? "..." : ""}
                </span>
              </Badge>
            )}
            {ad.objective && (
              <Badge
                variant="secondary"
                className="text-xs bg-secondary/80 text-secondary-foreground px-2 py-0.5"
              >
                <Zap className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                <span className="hidden sm:inline">{ad.objective}</span>
                <span className="sm:hidden">
                  {ad.objective.slice(0, 4)}
                  {ad.objective.length > 4 ? "..." : ""}
                </span>
              </Badge>
            )}
          </div>
        </div>

        {(ad.title || ad.brandName) && (
          <CardTitle className="text-sm sm:text-lg line-clamp-2 mt-2 sm:mt-3 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-300 leading-tight">
            {ad.title || ad.brandName}
          </CardTitle>
        )}

        <div className="text-xs text-muted-foreground bg-muted/50 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full inline-block w-fit mt-2">
          ID: {ad.id}
        </div>
      </CardHeader>

      <CardContent className="relative space-y-3 sm:space-y-6 p-3 sm:p-6 pt-0">
        {/* Video/Thumbnail with enhanced styling */}
        {(ad.videoUrl || ad.thumbnail || ad.thumbnailUrl) && (
          <div className="relative aspect-video rounded-lg sm:rounded-xl overflow-hidden bg-gradient-to-br from-muted/20 to-muted/40 shadow-inner">
            {ad.videoUrl ? (
              <video
                src={ad.videoUrl}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                controls
                preload="metadata"
                poster={ad.thumbnail || ad.thumbnailUrl}
              />
            ) : (
              <img
                src={ad.thumbnail || ad.thumbnailUrl}
                alt={ad.title || "YouTube ad thumbnail"}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
              />
            )}

            {/* Enhanced overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            {ad.duration && (
              <div className="absolute bottom-2 right-2 sm:bottom-3 sm:right-3 bg-black/90 backdrop-blur-md text-white px-2 py-1 sm:px-3 sm:py-1.5 rounded-full text-xs flex items-center gap-1 sm:gap-1.5 shadow-xl transition-transform duration-300 group-hover:scale-110">
                <Clock className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                {ad.duration}
              </div>
            )}

            {/* Play button overlay for YouTube videos */}
            {ad.videoUrl && (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
                <div className="bg-white/20 backdrop-blur-sm rounded-full p-3 sm:p-4 shadow-2xl transform scale-75 group-hover:scale-100 transition-transform duration-300">
                  <Play className="w-6 h-6 sm:w-8 sm:h-8 text-white fill-white" />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Ad Description with better styling */}
        {(ad.description || ad.adCopy) && (
          <div className="bg-muted/30 p-3 sm:p-4 rounded-lg sm:rounded-xl border-l-4 border-primary/30 transition-colors duration-300 group-hover:bg-muted/40">
            <p className="text-xs sm:text-sm text-muted-foreground line-clamp-3 leading-relaxed">
              {ad.description || ad.adCopy}
            </p>
          </div>
        )}

        {/* YouTube Stats Grid */}
        <div className="grid grid-cols-2 gap-2 sm:gap-3">
          {ad.views && (
            <div className="bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-950/30 dark:to-indigo-950/30 p-2 sm:p-3 rounded-lg sm:rounded-xl border border-blue-200/30 dark:border-blue-800/30 transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer">
              <div className="flex items-center gap-1 sm:gap-2">
                <Eye className="w-3 h-3 sm:w-4 sm:h-4 text-blue-500" />
                <span className="font-semibold text-foreground text-xs sm:text-sm">
                  {formatNumber(ad.views)}
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-0.5 sm:mt-1">
                Views
              </div>
            </div>
          )}

          {ad.estimatedSpend && (
            <div className="bg-gradient-to-br from-green-50/80 to-emerald-50/80 dark:from-green-950/30 dark:to-emerald-950/30 p-2 sm:p-3 rounded-lg sm:rounded-xl border border-green-200/30 dark:border-green-800/30 transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer">
              <div className="flex items-center gap-1 sm:gap-2">
                <DollarSign className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
                <span className="font-semibold text-foreground text-xs sm:text-sm">
                  {ad.estimatedSpend}
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-0.5 sm:mt-1">
                Est. Spend
              </div>
            </div>
          )}

          {ad.averageViewsPerDay && (
            <div className="bg-gradient-to-br from-orange-50/80 to-amber-50/80 dark:from-orange-950/30 dark:to-amber-950/30 p-2 sm:p-3 rounded-lg sm:rounded-xl border border-orange-200/30 dark:border-orange-800/30 transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer">
              <div className="flex items-center gap-1 sm:gap-2">
                <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4 text-orange-500" />
                <span className="font-semibold text-foreground text-xs sm:text-sm">
                  {formatNumber(ad.averageViewsPerDay)}
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-0.5 sm:mt-1">
                Daily Views
              </div>
            </div>
          )}

          {ad.runDuration && (
            <div className="bg-gradient-to-br from-purple-50/80 to-violet-50/80 dark:from-purple-950/30 dark:to-violet-950/30 p-2 sm:p-3 rounded-lg sm:rounded-xl border border-purple-200/30 dark:border-purple-800/30 transition-all duration-300 hover:shadow-lg hover:scale-105 cursor-pointer">
              <div className="flex items-center gap-1 sm:gap-2">
                <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-purple-500" />
                <span className="font-semibold text-foreground text-xs sm:text-sm">
                  {ad.runDuration}
                </span>
              </div>
              <div className="text-xs text-muted-foreground mt-0.5 sm:mt-1">
                Duration
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Additional Info */}
        <div className="space-y-3 sm:space-y-4">
          {ad.language && (
            <div className="flex items-center gap-2 text-xs sm:text-sm">
              <Globe className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium text-foreground">Language:</span>
              <Badge
                variant="outline"
                className="border-primary/30 text-primary text-xs px-2 py-0.5"
              >
                {ad.language}
              </Badge>
            </div>
          )}

          {ad.countries && ad.countries.length > 0 && (
            <div className="flex items-start gap-2 text-xs sm:text-sm">
              <Users className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <span className="font-medium text-foreground">Countries: </span>
                <div className="flex flex-wrap gap-1 mt-1 sm:mt-2">
                  {ad.countries.slice(0, 4).map((country) => (
                    <Badge
                      key={country}
                      variant="secondary"
                      className="text-xs bg-secondary/60 text-secondary-foreground px-1.5 py-0.5"
                    >
                      {country}
                    </Badge>
                  ))}
                  {ad.countries.length > 4 && (
                    <Badge
                      variant="outline"
                      className="text-xs border-muted-foreground/30 px-1.5 py-0.5"
                    >
                      +{ad.countries.length - 4}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 gap-2 sm:gap-3 text-xs sm:text-sm">
            {ad.dateRange && (
              <div className="flex items-center gap-2">
                <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
                <span className="text-muted-foreground">Range:</span>
                <span className="font-medium text-foreground truncate">
                  {ad.dateRange}
                </span>
              </div>
            )}

            {ad.lastSeen && (
              <div className="flex items-center gap-2">
                <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
                <span className="text-muted-foreground">Last Seen:</span>
                <span className="font-medium text-foreground truncate">
                  {formatDate(ad.lastSeen)}
                </span>
              </div>
            )}

            {ad.channelName && (
              <div className="flex items-center gap-2">
                <Users className="w-3 h-3 sm:w-4 sm:h-4 text-muted-foreground flex-shrink-0" />
                <span className="text-muted-foreground">Channel:</span>
                <span className="font-medium text-foreground truncate">
                  {ad.channelName}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Action Buttons */}
        <Separator className="bg-border/50" />
        <div className="flex flex-wrap gap-2">
          {ad.youtubeUrl && (
            <Button
              size="sm"
              className="bg-red-600 hover:bg-red-700 text-white shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 text-xs sm:text-sm px-3 py-2 h-8 sm:h-9"
              onClick={() => window.open(ad.youtubeUrl, "_blank")}
            >
              <Play className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5" />
              Watch
            </Button>
          )}

          {ad.youtubeChannelUrl && (
            <Button
              size="sm"
              variant="outline"
              className="shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 border-border hover:border-primary/50 text-xs sm:text-sm px-3 py-2 h-8 sm:h-9"
              onClick={() => window.open(ad.youtubeChannelUrl, "_blank")}
            >
              <ExternalLink className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5" />
              Channel
            </Button>
          )}

          <Button
            size="sm"
            variant="ghost"
            className="shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:bg-muted/80 text-xs sm:text-sm px-3 py-2 h-8 sm:h-9"
            onClick={() =>
              copyToClipboard(ad.youtubeUrl || ad.videoUrl || ad.id)
            }
          >
            <Copy className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5" />
            Copy
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default YoutubeCard;
