import React, { useState } from "react";
import ProfileSettings from "./Profile";
import PayoutSettings from "./Payout";

type SettingsTab = "profile" | "payment";

const SettingsContainer: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SettingsTab>("profile");

  const tabs = [
    { id: "profile" as SettingsTab, name: "Profile", label: "Profile" },
    { id: "payment" as SettingsTab, name: "Payment", label: "Payment" },
  ];

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6 sm:space-y-8">
      {/* Page Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-lg sm:text-xl">⚙️</span>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
            Settings
          </h1>
        </div>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
          Manage your profile and payment preferences.
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex flex-wrap gap-2 sm:gap-0 sm:space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-3 sm:py-2 px-4 sm:px-1 border-b-2 font-medium text-sm transition-colors min-h-[44px] sm:min-h-[36px] rounded-t-md sm:rounded-none ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 sm:bg-transparent"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 sm:hover:bg-transparent"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === "profile" && <ProfileSettings />}
        {activeTab === "payment" && <PayoutSettings />}
      </div>
    </div>
  );
};

export default SettingsContainer;
