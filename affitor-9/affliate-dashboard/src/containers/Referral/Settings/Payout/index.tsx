import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectUserData,
  selectUserIsUpdating,
  selectUserError,
} from "@/features/selectors";
import { actions as userActions } from "@/features/user/user.slice";
import {
  CreditCard,
  DollarSign,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

const PayoutSettings: React.FC = () => {
  const dispatch = useDispatch();
  const userData = useSelector(selectUserData);
  const isUpdating = useSelector(selectUserIsUpdating);
  const userError = useSelector(selectUserError);

  const [paypalEmail, setPaypalEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Load existing PayPal email from user data
  useEffect(() => {
    if (userData?.paypal_email) {
      setPaypalEmail(userData.paypal_email);
    }
  }, [userData]);

  // Handle update completion
  useEffect(() => {
    if (!isUpdating && isLoading) {
      setIsLoading(false);
      if (!userError) {
        setSaveSuccess(true);
        // Hide success message after 3 seconds
        const timer = setTimeout(() => {
          setSaveSuccess(false);
        }, 3000);
        return () => clearTimeout(timer);
      }
    }
  }, [isUpdating, userError, isLoading]);

  // Handle errors
  useEffect(() => {
    if (userError && isLoading) {
      setIsLoading(false);
      setErrors({ paypalEmail: userError });
    }
  }, [userError, isLoading]);

  const validatePaypalEmail = (email: string): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!email.trim()) {
      newErrors.paypalEmail = "PayPal email is required";
    } else if (!/^\S+@\S+\.\S+$/.test(email)) {
      newErrors.paypalEmail = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSavePayment = () => {
    if (validatePaypalEmail(paypalEmail)) {
      setIsLoading(true);
      setSaveSuccess(false);

      // Format data for API (same as onboarding)
      const updateData = {
        paypal_email: paypalEmail,
      };

      // Dispatch the same action used in onboarding
      dispatch(userActions.updateUserProfile(updateData));
    }
  };

  const handleEmailChange = (value: string) => {
    setPaypalEmail(value);
    // Clear success message when user starts editing
    if (saveSuccess) {
      setSaveSuccess(false);
    }
    // Clear errors when typing
    if (errors.paypalEmail) {
      setErrors({});
    }
  };

  return (
    <div className="space-y-8">
      {/* Success/Error Messages */}
      {saveSuccess && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 flex items-center">
          <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
          <p className="text-green-700 dark:text-green-300">
            Payment settings updated successfully!
          </p>
        </div>
      )}

      {errors.paypalEmail && !saveSuccess && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mr-3" />
          <p className="text-red-700 dark:text-red-300">{errors.paypalEmail}</p>
        </div>
      )}

      {/* Payment Method Section */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Payment Method
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Choose how you want to receive your commissions.
        </p>

        {/* PayPal Section */}
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">
                PayPal
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Receive payments via PayPal
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                PayPal Email
              </label>
              <input
                type="email"
                value={paypalEmail}
                onChange={(e) => handleEmailChange(e.target.value)}
                className={`w-full px-3 py-2 border ${
                  errors.paypalEmail
                    ? "border-red-500 dark:border-red-500"
                    : "border-gray-300 dark:border-gray-600"
                } rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                placeholder="<EMAIL>"
                disabled={isLoading || isUpdating}
              />
              {errors.paypalEmail && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {errors.paypalEmail}
                </p>
              )}
            </div>

            <button
              onClick={handleSavePayment}
              disabled={isLoading || isUpdating || !paypalEmail.trim()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading || isUpdating ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving Payment Settings...
                </>
              ) : (
                "Save Payment Settings"
              )}
            </button>
          </div>

          <div className="flex items-center mt-4 text-blue-600 bg-blue-100/50 dark:bg-blue-900/20 p-3 rounded">
            <span className="text-yellow-500 mr-2">💡</span>
            <p className="text-sm">Fastest and most secure payment method</p>
          </div>
        </div>
      </div>

      {/* Payout Settings */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          Payout Settings
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Current payout configuration and minimum payment threshold.
        </p>

        <div className="space-y-4">
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                <DollarSign className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Minimum Payout
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Current minimum: $50.00
                </p>
              </div>
              <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                Active
              </span>
            </div>
          </div>

          {/* Payment Status */}
          <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <CreditCard className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Payment Method Status
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {paypalEmail || userData?.paypal_email
                    ? `PayPal configured: ${
                        paypalEmail || userData?.paypal_email
                      }`
                    : "No payment method configured"}
                </p>
              </div>
              <span
                className={`text-sm font-medium ${
                  paypalEmail || userData?.paypal_email
                    ? "text-green-600 dark:text-green-400"
                    : "text-yellow-600 dark:text-yellow-400"
                }`}
              >
                {paypalEmail || userData?.paypal_email
                  ? "Ready"
                  : "Setup Required"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PayoutSettings;
