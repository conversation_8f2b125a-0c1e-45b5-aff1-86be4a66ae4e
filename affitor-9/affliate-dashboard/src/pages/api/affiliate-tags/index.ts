// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { AppError, ITag } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ITag[] | AppError>
) {
  try {
    const response: any = await StrapiClient.getAffiliateTags();
    
    // Check if response and response.data exist
    if (!response || !response.data) {
      throw new Error("Invalid response structure from Strapi");
    }
    
    const tags: ITag[] = Array.isArray(response.data) ? response.data : 
                         (response.data.data ? response.data.data : []);

    res.status(200).json(tags);
  } catch (error: any) {
    console.error("Error fetching affiliate tags:", error);
    sendApiError(res, error, "Error fetching affiliate tags");
  }
}
