import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST requests
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get token from request headers
    const token = req.headers.authorization?.split(" ")[1];

    if (!token) {
      return res.status(401).json({ error: "Authorization token is required" });
    }

    // Use the dedicated cancelSubscription method from StrapiClient
    const response = await StrapiClient.cancelSubscription(token);

    // Return the response from the backend
    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error canceling subscription:", error);

    return res.status(error.statusCode || 500).json({
      error: error.message || "Failed to cancel subscription",
    });
  }
}
