import { StrapiAdminClient } from "@/utils/request";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Authorization header required" });
    }
    const token = authHeader.replace("Bearer ", "");

    // Extract query parameters
    const { page = 1, pageSize = 10, sort = "id:ASC", search = "" } = req.query;

    // Use StrapiAdminClient.getReferralCommissions to fetch commissions
    const data = (await StrapiAdminClient.getReferralCommissions(
      {
        page: Number(page),
        pageSize: Number(pageSize),
        sort: sort as string,
        search: search as string,
      },
      token
    )) as any;

    // Transform the data to match our expected format
    const transformedData = {
      data:
        data.results?.map((commission: any) => ({
          id: commission.id,
          documentId: commission.documentId,
          commission_amount: commission.commission_amount,
          commission_status: commission.commission_status,
          commission_percentage: commission.commission_percentage,
          review_due_date: commission.review_due_date,
          payment_date: commission.payment_date,
          gross_sale_amount: commission.gross_sale_amount,
          createdAt: commission.createdAt,
          updatedAt: commission.updatedAt,
          publishedAt: commission.publishedAt,
          referrer: commission.referrer
            ? {
                ...commission.referrer,
                user: commission.referrer.user
                  ? {
                      first_name: commission.referrer.user.first_name,
                      last_name: commission.referrer.user.last_name,
                      email: commission.referrer.user.email,
                      username: commission.referrer.user.username,
                    }
                  : null,
              }
            : null,
          referral: commission.referral
            ? {
                ...commission.referral,
                user: commission.referral.user
                  ? {
                      first_name: commission.referral.user.first_name,
                      last_name: commission.referral.user.last_name,
                      email: commission.referral.user.email,
                      username: commission.referral.user.username,
                    }
                  : null,
              }
            : null,
          subscription_tier: commission.subscription_tier
            ? {
                id: commission.subscription_tier.id,
                name: commission.subscription_tier.name,
                display_name: commission.subscription_tier.display_name,
              }
            : null,
        })) || [],
      meta: {
        pagination: data.pagination || {
          page: parseInt(page.toString()),
          pageSize: parseInt(pageSize.toString()),
          pageCount: 0,
          total: 0,
        },
      },
    };

    res.status(200).json(transformedData);
  } catch (error: any) {
    res.status(500).json({
      error: "Failed to fetch referral commissions",
      details: error.message,
    });
  }
}
