import { NextApiRequest, NextApiResponse } from "next";
import { StrapiAdminClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: "Authorization header required" });
    }

    const token = authHeader.replace("Bearer ", "");

    // Extract query parameters
    const {
      page = 1,
      pageSize = 10,
      search = "",
      sort = "createdAt:DESC",
    } = req.query;

    // Use StrapiAdminClient.getReferralTransaction to fetch transactions
    const data = (await StrapiAdminClient.getReferralTransaction(
      {
        page: Number(page),
        pageSize: Number(pageSize),
        search: search as string,
        sort: sort as string,
      },
      token
    )) as any;

    // Transform the data to match our expected format
    const transformedData = {
      data:
        data.results?.map((transaction: any) => ({
          id: transaction.id,
          documentId: transaction.documentId,
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt,
          publishedAt: transaction.publishedAt,
          amount: transaction.amount,
          currency: transaction.currency,
          payment_status: transaction.payment_status,
          payment_method: transaction.payment_method,
          transaction_date: transaction.transaction_date,
          payment_details: transaction.payment_details,
          stripe_checkout_session: transaction.stripe_checkout_session,
          stripe_subscription_id: transaction.stripe_subscription_id,
          stripe_customer_id: transaction.stripe_customer_id,
          stripe_price_id: transaction.stripe_price_id,
          stripe_invoice_id: transaction.stripe_invoice_id,
          current_period_start: transaction.current_period_start,
          current_period_end: transaction.current_period_end,
          auto_renew: transaction.auto_renew,
          cancellation_date: transaction.cancellation_date,
          cancellation_reason: transaction.cancellation_reason,
          is_checked_subscription: transaction.is_checked_subscription,
          user: transaction.user,
          subscription_tier: transaction.subscription_tier,
          child_transactions: transaction.child_transactions,
          parent_transaction: transaction.parent_transaction,
          status: transaction.status,
        })) || [],
      meta: data.pagination || {
        page: 1,
        pageSize: 10,
        pageCount: 0,
        total: 0,
      },
    };

    res.status(200).json(transformedData);
  } catch (error: any) {
    console.error("Error fetching admin transactions:", error);
    res.status(500).json({
      error: error.message || "Failed to fetch transactions",
    });
  }
}
