import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { createApiContext } from "@/utils/api-middleware";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // Only allow POST for registration
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Use StrapiClient instead of direct axios call
    const response = await StrapiClient.registerReferrer(token!);

    // Return the response from Strapi
    return res.status(200).json(response);
  } catch (error: any) {
    console.error("Error registering referrer:", error);

    // Return appropriate error
    const statusCode = error.response?.status || 500;
    const errorMessage =
      error.response?.data?.error?.message ||
      error.message ||
      "Failed to register as referrer";

    return res.status(statusCode).json({
      error: errorMessage,
    });
  }
}
