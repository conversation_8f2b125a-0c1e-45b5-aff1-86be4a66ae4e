import type { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (!token) {
      return res.status(401).json({ message: "Authentication required" });
    }

    const response = await StrapiClient.getReferralCommissionStats(token);

    res.status(200).json(response);
  } catch (error: any) {
    console.error("API Error:", error);
    res.status(error.statusCode || 500).json({
      message: error.message || "Internal server error",
    });
  }
}
