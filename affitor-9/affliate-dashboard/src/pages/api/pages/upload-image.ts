import { NextApiRequest, NextApiResponse } from "next";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import formidable from 'formidable';
import fs from 'fs';

// Disable default body parser to handle multipart/form-data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token } = createApiContext(req, { requireAuth: true });

    // Parse the multipart form data
    const form = formidable({
      maxFileSize: 5 * 1024 * 1024, // 5MB limit
      allowEmptyFiles: false,
      filter: ({ mimetype }) => {
        // Only allow image files
        return Boolean(mimetype && mimetype.startsWith('image/'));
      },
    });

    const [fields, files] = await form.parse(req);
    
    // Validate that we have an image file
    if (!files.image || !files.image[0]) {
      return res.status(400).json({ 
        error: "No image file provided",
        statusCode: 400 
      });
    }

    const imageFile = files.image[0];
    const pageId = fields.pageId?.[0];

    if (!pageId) {
      return res.status(400).json({ 
        error: "Page ID is required",
        statusCode: 400 
      });
    }

    console.log('📸 [Upload Image API] Processing upload:', {
      fileName: imageFile.originalFilename,
      fileSize: imageFile.size,
      mimeType: imageFile.mimetype,
      pageId,
    });

    // Create FormData for Strapi request
    const FormData = require('form-data');
    const formData = new FormData();
    
    // Add the image file
    formData.append('image', fs.createReadStream(imageFile.filepath), {
      filename: imageFile.originalFilename,
      contentType: imageFile.mimetype,
    });
    
    // Add the page ID
    formData.append('pageId', pageId);

    // Forward request to Strapi
    const response: any = await StrapiClient.client.post('/api/pages/upload-image', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        ...formData.getHeaders(),
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
    });

    console.log('📸 [Upload Image API] Strapi response:', {
      responseData: response, // Log the full response for debugging
      responseKeys: response ? Object.keys(response) : 'No response',
      hasS3Url: !!response?.s3Url,
    });

    // Clean up temporary file
    try {
      fs.unlinkSync(imageFile.filepath);
    } catch (cleanupError) {
      console.warn('Failed to clean up temporary file:', cleanupError);
    }

    // Return the S3 URL to the frontend
    // Note: StrapiClient.client.post() returns response.data directly, not wrapped
    const s3Url = response?.s3Url;
    const fileName = response?.fileName;
    const fileKey = response?.fileKey;

    if (!s3Url) {
      console.error('📸 [Upload Image API] Missing s3Url in response:', response);
      return res.status(500).json({
        error: "Upload completed but S3 URL not found in response",
        statusCode: 500
      });
    }

    return res.status(200).json({
      s3Url,
      fileName,
      fileKey,
    });

  } catch (error: any) {
    console.error("Upload Image API error:", error);
    
    // Handle specific error cases
    if (error.response?.status === 413) {
      return res.status(413).json({
        error: "File too large. Maximum size is 5MB.",
        statusCode: 413
      });
    }
    
    if (error.response?.status === 400) {
      return res.status(400).json({
        error: error.response.data?.error || "Invalid request",
        statusCode: 400
      });
    }
    
    if (error.response?.status === 403) {
      return res.status(403).json({
        error: "You do not have permission to upload images for this page",
        statusCode: 403
      });
    }

    return sendApiError(res, error, "Failed to upload image");
  }
}
