import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { IMeta, IPagination, ISort } from "@/interfaces";

export interface ReferralActivity {
  id: number;
  documentId: string;
  amount_paid: number | null;
  referral_status: "lead" | "conversion";
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}

interface ReferralActivityState {
  activities: ReferralActivity[];
  loading: boolean;
  error: string | null;
  meta: IMeta | null;
}

const initialState: ReferralActivityState = {
  activities: [],
  loading: false,
  error: null,
  meta: null,
};

const referralActivitySlice = createSlice({
  name: "referralActivity",
  initialState,
  reducers: {
    // Fetch activities
    fetchActivitiesRequest: (
      state,
      action: PayloadAction<{
        pagination?: IPagination;
        sort?: ISort;
      }>
    ) => {
      state.loading = true;
      state.error = null;
    },
    fetchActivitiesSuccess: (
      state,
      action: PayloadAction<{
        data: ReferralActivity[];
        meta: IMeta;
      }>
    ) => {
      state.loading = false;
      state.activities = action.payload.data;
      state.meta = action.payload.meta;
      state.error = null;
    },
    fetchActivitiesFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Clear state
    clearActivitiesState: (state) => {
      state.activities = [];
      state.meta = null;
      state.error = null;
      state.loading = false;
    },
  },
});

export const { actions } = referralActivitySlice;
export const { reducer } = referralActivitySlice;
export default referralActivitySlice.reducer;
