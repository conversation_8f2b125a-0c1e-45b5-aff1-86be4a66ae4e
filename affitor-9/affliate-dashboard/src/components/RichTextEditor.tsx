"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import type { YooptaContentValue } from '@/types/yoopta-editor';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Image,
  Save,
  Loader
} from 'lucide-react';

interface RichTextEditorProps {
  content?: YooptaContentValue | string;
  onChange?: (content: YooptaContentValue) => void;
  onSave?: (content: YooptaContentValue) => void;
  autoSave?: boolean;
  autoSaveInterval?: number;
  loading?: boolean;
  className?: string;
  placeholder?: string;
}

// Helper functions for Yoopta content conversion
const convertYooptaToMarkdown = (content: YooptaContentValue): string => {
  if (!content || typeof content !== 'object') {
    return '';
  }

  let markdown = '';

  // Sort blocks by order if meta.order exists
  const blocks = Object.values(content).sort((a, b) => {
    const orderA = a.meta?.order || 0;
    const orderB = b.meta?.order || 0;
    return orderA - orderB;
  });

  for (const block of blocks) {
    const text = extractTextFromValue(block.value);

    switch (block.type) {
      case 'paragraph':
        markdown += `${text}\n\n`;
        break;
      case 'heading-one':
        markdown += `# ${text}\n\n`;
        break;
      case 'heading-two':
        markdown += `## ${text}\n\n`;
        break;
      case 'heading-three':
        markdown += `### ${text}\n\n`;
        break;
      case 'blockquote':
        markdown += `> ${text}\n\n`;
        break;
      case 'bulleted-list':
        markdown += `- ${text}\n`;
        break;
      case 'numbered-list':
        markdown += `1. ${text}\n`;
        break;
      case 'code':
        markdown += `\`\`\`\n${text}\n\`\`\`\n\n`;
        break;
      default:
        markdown += `${text}\n\n`;
    }
  }

  return markdown.trim();
};

const extractTextFromValue = (value: any[]): string => {
  if (!Array.isArray(value)) {
    return '';
  }

  return value.map(item => {
    if (typeof item === 'string') {
      return item;
    }
    if (item && typeof item === 'object' && item.text) {
      return item.text;
    }
    if (item && typeof item === 'object' && item.children) {
      return extractTextFromValue(item.children);
    }
    return '';
  }).join('');
};

const convertMarkdownToYoopta = (markdown: string): YooptaContentValue => {
  const lines = markdown.split('\n');
  const blocks: YooptaContentValue = {};
  let order = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;

    const blockId = `block-${Date.now()}-${order}`;
    let type = 'paragraph';
    let text = line;

    // Detect block type
    if (line.startsWith('# ')) {
      type = 'heading-one';
      text = line.substring(2);
    } else if (line.startsWith('## ')) {
      type = 'heading-two';
      text = line.substring(3);
    } else if (line.startsWith('### ')) {
      type = 'heading-three';
      text = line.substring(4);
    } else if (line.startsWith('> ')) {
      type = 'blockquote';
      text = line.substring(2);
    } else if (line.startsWith('- ')) {
      type = 'bulleted-list';
      text = line.substring(2);
    } else if (/^\d+\. /.test(line)) {
      type = 'numbered-list';
      text = line.replace(/^\d+\. /, '');
    }

    blocks[blockId] = {
      id: blockId,
      type,
      value: [{ text }],
      meta: { order, depth: 0 }
    };

    order++;
  }

  return blocks;
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  content,
  onChange,
  onSave,
  autoSave = true,
  autoSaveInterval = 5000,
  loading = false,
  className = '',
  placeholder = 'Start writing your content...'
}) => {
  // Convert content to markdown for editing
  const initialMarkdown = typeof content === 'string'
    ? content
    : content
      ? convertYooptaToMarkdown(content)
      : '';

  const [editorContent, setEditorContent] = useState<string>(initialMarkdown);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle content changes
  const handleContentChange = useCallback((value: string) => {
    setEditorContent(value);
    setHasUnsavedChanges(true);

    if (onChange) {
      // Convert markdown to Yoopta format
      const yooptaContent = convertMarkdownToYoopta(value);
      onChange(yooptaContent);
    }

    // Auto-save functionality
    if (autoSave && onSave) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }

      autoSaveTimeoutRef.current = setTimeout(() => {
        handleSave();
      }, autoSaveInterval);
    }
  }, [onChange, onSave, autoSave, autoSaveInterval]);

  // Handle save
  const handleSave = useCallback(() => {
    if (onSave) {
      const yooptaContent = convertMarkdownToYoopta(editorContent);
      onSave(yooptaContent);
      setHasUnsavedChanges(false);
    }
  }, [editorContent, onSave]);

  // Format text functions
  const insertText = useCallback((before: string, after: string = '') => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = editorContent.substring(start, end);
    
    const newText = editorContent.substring(0, start) + 
                   before + selectedText + after + 
                   editorContent.substring(end);
    
    handleContentChange(newText);
    
    // Restore cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(
        start + before.length, 
        start + before.length + selectedText.length
      );
    }, 0);
  }, [editorContent, handleContentChange]);

  // Toolbar actions
  const formatBold = () => insertText('**', '**');
  const formatItalic = () => insertText('*', '*');
  const formatUnderline = () => insertText('<u>', '</u>');
  const formatH1 = () => insertText('# ');
  const formatH2 = () => insertText('## ');
  const formatH3 = () => insertText('### ');
  const formatList = () => insertText('- ');
  const formatOrderedList = () => insertText('1. ');
  const formatQuote = () => insertText('> ');
  const formatLink = () => insertText('[', '](url)');
  const formatImage = () => insertText('![alt text](', ')');

  // Keyboard shortcuts
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          formatBold();
          break;
        case 'i':
          e.preventDefault();
          formatItalic();
          break;
        case 's':
          e.preventDefault();
          handleSave();
          break;
      }
    }
  }, [formatBold, formatItalic, handleSave]);

  // Update content when prop changes
  useEffect(() => {
    const newMarkdown = typeof content === 'string'
      ? content
      : content
        ? convertYooptaToMarkdown(content)
        : '';

    if (newMarkdown !== editorContent) {
      setEditorContent(newMarkdown);
      setHasUnsavedChanges(false);
    }
  }, [content]);

  // Cleanup auto-save timeout
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);



  return (
    <div className={`rich-text-editor ${className}`}>
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-2 border-b bg-gray-50 dark:bg-gray-800 rounded-t-md">
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={formatBold}
            disabled={loading}
            title="Bold (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatItalic}
            disabled={loading}
            title="Italic (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatUnderline}
            disabled={loading}
            title="Underline"
          >
            <Underline className="h-4 w-4" />
          </Button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={formatH1}
            disabled={loading}
            title="Heading 1"
          >
            <Heading1 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatH2}
            disabled={loading}
            title="Heading 2"
          >
            <Heading2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatH3}
            disabled={loading}
            title="Heading 3"
          >
            <Heading3 className="h-4 w-4" />
          </Button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={formatList}
            disabled={loading}
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatOrderedList}
            disabled={loading}
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatQuote}
            disabled={loading}
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </Button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={formatLink}
            disabled={loading}
            title="Link"
          >
            <Link className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={formatImage}
            disabled={loading}
            title="Image"
          >
            <Image className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex-1" />

        <div className="flex items-center gap-2">
          {onSave && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={loading || !hasUnsavedChanges}
              title="Save (Ctrl+S)"
            >
              {loading ? (
                <Loader className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {hasUnsavedChanges && <span className="ml-1 text-orange-500">•</span>}
            </Button>
          )}
        </div>
      </div>

      {/* Editor Content */}
      <div className="relative">
        <textarea
            ref={textareaRef}
            value={editorContent}
            onChange={(e) => handleContentChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={loading}
            className="w-full min-h-[400px] p-4 border-0 resize-none focus:outline-none focus:ring-0 font-mono text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            style={{ fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace' }}
          />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-4 py-2 text-xs text-gray-500 dark:text-gray-400 border-t bg-gray-50 dark:bg-gray-800 rounded-b-md">
        <div className="flex items-center gap-4">
          <span>{editorContent.length} characters</span>
          <span>{editorContent.split('\n').length} lines</span>
        </div>
        
        <div className="flex items-center gap-2">
          {hasUnsavedChanges && (
            <span className="text-orange-500">Unsaved changes</span>
          )}
          {autoSave && (
            <span className="text-green-500">Auto-save enabled</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor;
