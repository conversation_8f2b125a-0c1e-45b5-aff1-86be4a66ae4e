"use client";

import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { X } from 'lucide-react';
import { actions, selectSavePromptModal } from '@/features/aiscript/aiscript.slice';

interface SavePromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialContent?: string;
}

export default function SavePromptModal({ isOpen, onClose, initialContent = '' }: SavePromptModalProps) {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    name: '',
    content: initialContent,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        name: '',
        content: initialContent,
      });
    }
  }, [isOpen, initialContent]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.content.trim()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Create the prompt
      await dispatch(actions.createUserPrompt({
        title: formData.name.trim(),
        content: formData.content.trim(),
        description: '',
        tags: [],
        isFavorite: false,
      }));
      
      // Close modal and refresh prompts
      onClose();
      dispatch(actions.fetchUserPrompts());
    } catch (error) {
      console.error('Error saving prompt:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({ name: '', content: '' });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-xl w-full max-w-lg">
        {/* Header */}
        <div className="text-center p-6 pb-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Save Prompt
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Create a reusable prompt for future use
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="px-6 pb-6">
          <div className="space-y-4">
            {/* Prompt Name */}
            <div>
              <label htmlFor="prompt-name" className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Prompt Name
              </label>
              <input
                type="text"
                id="prompt-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., YouTube Video Script, Product Review..."
                required
              />
            </div>

            {/* Prompt Content */}
            <div>
              <label htmlFor="prompt-content" className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Prompt Content
              </label>
              <textarea
                id="prompt-content"
                rows={6}
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                placeholder="Enter your prompt text here..."
                required
              />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex items-center justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2.5 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !formData.name.trim() || !formData.content.trim()}
              className="px-6 py-2.5 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
            >
              {isSubmitting ? 'Saving...' : 'Save Prompt'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
