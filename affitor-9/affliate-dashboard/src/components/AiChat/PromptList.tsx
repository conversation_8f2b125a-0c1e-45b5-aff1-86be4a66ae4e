"use client";

import React from 'react';
import { useSelector } from 'react-redux';
import { Tag, AlertCircle, LogIn } from 'lucide-react';
import { UserPrompt } from '@/features/aiscript/aiscript.slice';
import { selectIsAuthenticated } from '@/features/auth/auth.slice';

interface PromptListProps {
  prompts: UserPrompt[];
  isLoading: boolean;
  error: string | null;
  onEdit: (prompt: UserPrompt) => void;
  onDelete: (promptId: string) => void;
  onToggleFavorite: (promptId: string) => void;
  onUse: (prompt: UserPrompt) => void;
  onViewDetail: (prompt: UserPrompt) => void;
  view?: 'all' | 'favorites' | 'recent' | 'templates';
}

export default function PromptList({
  prompts,
  isLoading,
  error,
  onEdit,
  onDelete,
  onToggleFavorite,
  onUse,
  onViewDetail,
  view = 'all',
}: PromptListProps) {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatUsageCount = (count: number) => {
    if (count === 0) return 'Never used';
    if (count === 1) return 'Used once';
    return `Used ${count} times`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-80">
        <div className="flex flex-col items-center gap-4 text-gray-500 dark:text-gray-400">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="text-lg font-medium">Loading prompts...</span>
          <p className="text-sm text-gray-400 dark:text-gray-500">Please wait while we fetch your saved prompts</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-80">
        <div className="flex flex-col items-center gap-4 text-red-500 dark:text-red-400 text-center max-w-sm">
          <AlertCircle size={32} />
          <h3 className="text-lg font-medium">Error loading prompts</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
          <p className="text-xs text-gray-500 dark:text-gray-500">
            Please try refreshing the page or contact support if the issue persists
          </p>
        </div>
      </div>
    );
  }

  if (prompts.length === 0) {
    // Show different messages based on the current view and authentication status
    if (view === 'templates') {
      return (
        <div className="flex flex-col items-center justify-center h-80 text-gray-500 dark:text-gray-400">
          <div className="text-center max-w-sm">
            <div className="w-20 h-20 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <Tag size={28} />
            </div>
            <h3 className="text-xl font-medium mb-3">Templates Coming Soon</h3>
            <p className="text-sm leading-relaxed mb-4">
              We're working on bringing you pre-built prompt templates to help you get started faster.
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500">
              In the meantime, you can create your own prompts in the "Your Saved" tab
            </p>
          </div>
        </div>
      );
    }

    // Show login requirement if user is not authenticated
    if (!isAuthenticated) {
      return (
        <div className="flex flex-col items-center justify-center h-full min-h-[300px] px-4">
          <div className="text-center max-w-xs">
            {/* Icon with gradient background */}
            <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
              <LogIn size={20} className="text-white" />
            </div>

            {/* Heading */}
            <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
              Sign In Required
            </h3>

            {/* Description */}
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 leading-relaxed">
              Access your saved prompts and create new ones
            </p>

            {/* CTA Button */}
            <button
              onClick={() => window.location.href = '/authentication'}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg text-sm"
            >
              Sign In
            </button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center h-80 text-gray-500 dark:text-gray-400 px-2">
        <div className="text-center max-w-sm">
          <div className="w-20 h-20 mx-auto mb-6 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <Tag size={28} />
          </div>
          <h3 className="text-xl font-medium mb-3">No prompts found</h3>
          <p className="text-sm leading-relaxed mb-4">Create your first prompt to get started with AI-powered conversations</p>
          <p className="text-xs text-gray-400 dark:text-gray-500">
            Tip: Use the "Create new prompt" button below to add your first prompt
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-2 animate-in fade-in-0 duration-200">
      <div className="space-y-3 pb-20">
        {prompts.map((prompt) => (
          <div
            key={prompt.id}
            onClick={() => onViewDetail(prompt)}
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md hover:bg-gray-50 dark:hover:bg-gray-750 transition-all duration-200 cursor-pointer group"
          >
            {/* Title */}
            <div className="mb-2">
              <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {prompt.title}
              </h3>
            </div>

            {/* Description/Preview */}
            <div className="mb-3">
              <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                {prompt.description || prompt.content}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
