import { IPaymentMethod } from "@/interfaces";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "./ui/tooltip";

export default function PaymentMethod({ method }: { method: IPaymentMethod }) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <img src={method.image?.url} alt={method.name} className="w-10 h-10" />
        </TooltipTrigger>
        <TooltipContent className="bg-secondary text-[15px]">
          <p>{method.name}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
