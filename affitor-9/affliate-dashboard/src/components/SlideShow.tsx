import React, { useEffect, useState } from 'react';

interface Slide {
  title: string;
  description: string;
  image: string;
}

interface SlideShowProps {
  slides: Slide[];
}

const SlideShow: React.FC<SlideShowProps> = ({ slides }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [slides.length]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <div className="relative h-full w-full overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-100">
      <div className="absolute inset-0 flex items-center justify-center">
        {slides.map((slide, index) => (
            <div
              key={index}
              className={`absolute py-10 inset-0 transition-opacity duration-1000 flex flex-col items-center justify-start lg:justify-center  px-12 
                ${index === currentSlide ? 'opacity-100' : 'opacity-0'}`}
            >
              <h2 className="text-2xl font-bold text-[#3861FB] mb-3">{slide.title}</h2>
              <p className="text-base text-center text-gray-700">{slide.description}</p>
            </div>
        ))}
      </div>
      
      <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`h-2 w-2 rounded-full ${
              index === currentSlide ? 'bg-[#3861FB]' : 'bg-gray-300'
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default SlideShow;
