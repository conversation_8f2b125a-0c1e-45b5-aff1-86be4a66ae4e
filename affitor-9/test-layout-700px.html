<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Test - 700px Height</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            width: 400px;
            height: 700px;
            border: 2px solid #333;
            background: white;
            position: relative;
            margin: 0 auto;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .prompt-manager-mock {
            height: min(75vh, 500px);
            max-height: min(75vh, 500px);
            min-height: 400px;
            display: flex;
            flex-direction: column;
            border: 1px solid #e5e7eb;
            background: white;
        }
        
        .header {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
            flex-shrink: 0;
        }
        
        .search-tabs {
            padding: 8px;
            border-bottom: 1px solid #e5e7eb;
            flex-shrink: 0;
        }
        
        .content-area {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }
        
        .footer {
            padding: 8px;
            border-top: 1px solid #e5e7eb;
            flex-shrink: 0;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .button:hover {
            background: #2563eb;
        }
        
        .prompt-item {
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            cursor: pointer;
        }
        
        .prompt-item:hover {
            background: #f9fafb;
        }
        
        .scrollable {
            overflow-y: auto;
            scrollbar-width: thin;
        }
        
        .scrollable::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollable::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .scrollable::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.5);
            border-radius: 3px;
        }
        
        .info {
            position: absolute;
            top: -40px;
            left: 0;
            right: 0;
            text-align: center;
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="info">Testing Layout at 700px Height</div>
    <div class="test-container">
        <div class="prompt-manager-mock">
            <div class="header">
                <h3>Prompts</h3>
            </div>
            
            <div class="search-tabs">
                <input type="text" placeholder="Search prompts..." style="width: 100%; padding: 8px; margin-bottom: 8px;">
                <div>Your Saved (5) | Templates</div>
            </div>
            
            <div class="content-area scrollable">
                <div class="prompt-item">
                    <h4>Sample Prompt 1</h4>
                    <p>This is a sample prompt content that demonstrates the layout...</p>
                </div>
                <div class="prompt-item">
                    <h4>Sample Prompt 2</h4>
                    <p>Another prompt to show scrolling behavior...</p>
                </div>
                <div class="prompt-item">
                    <h4>Sample Prompt 3</h4>
                    <p>More content to test vertical scrolling...</p>
                </div>
                <div class="prompt-item">
                    <h4>Sample Prompt 4</h4>
                    <p>Additional content for testing...</p>
                </div>
                <div class="prompt-item">
                    <h4>Sample Prompt 5</h4>
                    <p>Last item to ensure scrolling works properly...</p>
                </div>
                <div class="prompt-item">
                    <h4>Sample Prompt 6</h4>
                    <p>Extra content to force scrolling...</p>
                </div>
                <div class="prompt-item">
                    <h4>Sample Prompt 7</h4>
                    <p>More content to test the scroll behavior...</p>
                </div>
            </div>
            
            <div class="footer">
                <button class="button">Create New Prompt</button>
            </div>
        </div>
    </div>
    
    <script>
        // Simulate the height constraints we implemented
        const container = document.querySelector('.prompt-manager-mock');
        const actualHeight = container.offsetHeight;
        const viewportHeight = window.innerHeight;
        
        console.log('Container height:', actualHeight + 'px');
        console.log('Viewport height:', viewportHeight + 'px');
        console.log('Height as % of viewport:', Math.round((actualHeight / viewportHeight) * 100) + '%');
        
        // Test scrolling
        const contentArea = document.querySelector('.content-area');
        console.log('Content area scrollable height:', contentArea.scrollHeight + 'px');
        console.log('Content area visible height:', contentArea.offsetHeight + 'px');
        console.log('Scrollable content:', contentArea.scrollHeight > contentArea.offsetHeight ? 'Yes' : 'No');
    </script>
</body>
</html>
