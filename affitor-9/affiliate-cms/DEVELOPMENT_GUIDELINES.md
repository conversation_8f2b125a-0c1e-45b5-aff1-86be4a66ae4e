# Development Guidelines for Affiliate CMS

## Project Overview

This is a **Strapi 5.11.2** based CMS application built with **TypeScript**, **PostgreSQL**, and deployed on **AWS ECS**. The project follows a modular API structure with comprehensive features including affiliate management, referral systems, payment processing, AI integration, and social media listening.

## Technology Stack

- **Backend**: Strapi 5.11.2 (Node.js/TypeScript)
- **Database**: PostgreSQL
- **File Storage**: AWS S3
- **Payment**: Stripe
- **AI Services**: OpenAI, Anthropic Claude, Google Gemini
- **Social Media**: YouTube API, TikTok API
- **Infrastructure**: AWS ECS, Docker
- **Package Manager**: Yarn

## Project Structure

```
src/
├── api/                    # API modules (one per entity)
│   ├── {entity}/
│   │   ├── content-types/  # Schema definitions
│   │   ├── controllers/    # Request handlers
│   │   ├── services/       # Business logic
│   │   ├── routes/         # Route definitions
│   │   ├── interfaces/     # TypeScript interfaces
│   │   ├── middlewares/    # Entity-specific middleware
│   │   └── lifecycles.ts   # Database lifecycle hooks
├── middlewares/            # Global middleware
├── services/              # Global services
├── utils/                 # Utility functions
├── types/                 # Global TypeScript types
└── index.ts              # Application entry point

config/                    # Configuration files
types/generated/           # Auto-generated Strapi types
```

## API Module Structure Rules

### 1. Content Types (Schema Definition)

**Location**: `src/api/{entity}/content-types/{entity}/schema.json`

**Rules**:

- Use descriptive field names in snake_case
- Always include required validation where appropriate
- Use proper field types (string, integer, biginteger, boolean, enumeration, etc.)
- Define relations clearly with proper inversedBy/mappedBy
- Include media fields with allowedTypes specification
- Use components for reusable data structures

**Example**:

```json
{
  "kind": "collectionType",
  "collectionName": "affiliates",
  "info": {
    "singularName": "affiliate",
    "pluralName": "affiliates",
    "displayName": "Affiliate"
  },
  "attributes": {
    "name": {
      "type": "string",
      "required": true,
      "unique": true
    },
    "url": {
      "type": "string"
    },
    "categories": {
      "type": "relation",
      "relation": "manyToMany",
      "target": "api::category.category",
      "inversedBy": "affiliates"
    }
  }
}
```

### 2. Controllers

**Location**: `src/api/{entity}/controllers/{entity}.ts`

**Rules**:

- Always extend `factories.createCoreController`
- Implement proper authentication checks using `ctx.state.user`
- Use consistent error handling patterns
- Return standardized response formats
- Implement input validation
- Use try-catch blocks for error handling
- Log important operations using `strapi.log.info/error`

**Template**:

```typescript
import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::{entity}.{entity}', ({ strapi }) => ({
  async customAction(ctx) {
    try {
      // Authentication check
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      // Input validation
      const { requiredParam } = ctx.request.body;
      if (!requiredParam) {
        return ctx.badRequest('Required parameter missing');
      }

      // Business logic
      const result = await strapi.service('api::{entity}.{entity}').customMethod(requiredParam);

      // Return standardized response
      return { data: result };
    } catch (error) {
      strapi.log.error('Error in customAction:', error);
      return ctx.badRequest(error.message || 'An error occurred');
    }
  },
}));
```

### 3. Services

**Location**: `src/api/{entity}/services/{entity}.ts`

**Rules**:

- Always extend `factories.createCoreService`
- Implement business logic, not request handling
- Use proper TypeScript interfaces
- Handle database operations with proper error handling
- Use `strapi.entityService` for CRUD operations
- Use `strapi.db.query` for complex queries
- Implement proper logging

**Template**:

```typescript
import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::{entity}.{entity}', ({ strapi }) => ({
  async customMethod(param: string) {
    try {
      const result = await strapi.entityService.findMany('api::{entity}.{entity}', {
        filters: { field: param },
        populate: ['relation'],
      });

      return result;
    } catch (error) {
      console.error('Error in customMethod:', error);
      throw error;
    }
  },
}));
```

### 4. Routes

**Location**: `src/api/{entity}/routes/{entity}.ts` (default) or `custom-routes.ts` (custom)

**Rules**:

- Use `factories.createCoreRouter` for default CRUD routes
- Create separate `custom-routes.ts` for custom endpoints
- Follow RESTful conventions
- Use proper HTTP methods (GET, POST, PUT, DELETE)
- Include proper route configuration

**Default Routes**:

```typescript
import { factories } from '@strapi/strapi';

export default factories.createCoreRouter('api::{entity}.{entity}');
```

**Custom Routes**:

```typescript
export default {
  routes: [
    {
      method: 'GET',
      path: '/{entities}/custom-action',
      handler: '{entity}.customAction',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
```

### 5. Interfaces

**Location**: `src/api/{entity}/interfaces/index.ts`

**Rules**:

- Define TypeScript interfaces for all entities
- Use proper typing for relations
- Include optional fields with `?`
- Export interfaces for reuse
- Follow naming convention: `I{EntityName}`

**Template**:

```typescript
export interface IEntity {
  id?: number;
  name: string;
  optional_field?: string;

  // Relations
  related_entities?: IRelatedEntity[];

  // Strapi fields
  createdAt?: Date;
  updatedAt?: Date;
  publishedAt?: Date;
}
```

## Error Handling Standards

> **Reference**: [Strapi Error Handling Documentation](https://docs.strapi.io/cms/error-handling)

Strapi provides a standardized error handling system with specific error classes and response formats. This section covers both receiving and throwing errors in your application.

### 1. Error Response Formats

#### REST API Error Format

```typescript
{
  "data": null,
  "error": {
    "status": "", // HTTP status code
    "name": "", // Strapi error name ('ApplicationError' or 'ValidationError')
    "message": "", // Human readable error message
    "details": {
      // Error info specific to the error type
    }
  }
}
```

#### GraphQL API Error Format

```typescript
{
  "errors": [
    {
      "message": "", // Human readable error message
      "extensions": {
        "error": {
          "name": "", // Strapi error name
          "message": "", // Human readable error message
          "details": {}, // Error info specific to the error type
        },
        "code": "" // GraphQL error code (ex: BAD_USER_INPUT)
      }
    }
  ],
  "data": {
    "graphQLQueryName": null
  }
}
```

### 2. Controller and Middleware Error Handling

**Recommended approach**: Use context error functions for proper HTTP responses.

```typescript
// Available error functions (from http-errors, camelCased)
return ctx.badRequest('Error message', { details: 'object' });
return ctx.unauthorized('Authentication required');
return ctx.forbidden('Access denied');
return ctx.notFound('Resource not found');
return ctx.methodNotAllowed('Method not allowed');
return ctx.conflict('Resource conflict');
return ctx.unprocessableEntity('Validation failed');
return ctx.internalServerError('Server error');

// Example controller with comprehensive error handling
export default factories.createCoreController('api::entity.entity', ({ strapi }) => ({
  async customAction(ctx) {
    const { user } = ctx.state;

    // Authentication check
    if (!user) {
      return ctx.unauthorized('Authentication required');
    }

    try {
      const { requiredParam } = ctx.request.body;

      // Input validation
      if (!requiredParam) {
        return ctx.badRequest('Required parameter missing', {
          field: 'requiredParam',
          received: requiredParam,
        });
      }

      // URL validation example
      if (url) {
        try {
          new URL(url);
        } catch (urlError) {
          return ctx.badRequest('Invalid URL format', { url });
        }
      }

      // Check for duplicates
      const existing = await strapi.entityService.findMany('api::entity.entity', {
        filters: { field: requiredParam },
        limit: 1,
      });

      if (existing?.length > 0) {
        return ctx.conflict('Resource already exists', {
          existingId: existing[0].id,
        });
      }

      // Business logic
      const result = await strapi.service('api::entity.entity').customMethod(requiredParam);

      return { data: result };
    } catch (error) {
      strapi.log.error('Error in customAction:', error);

      // Handle specific validation errors
      if (error.name === 'YupValidationError') {
        const errorDetails = error.details?.errors || [];
        const errorMessages = errorDetails.map((err) => err.message || err).join(', ');
        return ctx.badRequest(`Validation error: ${errorMessages}`, {
          validationErrors: errorDetails,
        });
      }

      // Handle unique constraint violations
      if (error.message?.includes('unique constraint') || error.code === 'ER_DUP_ENTRY') {
        return ctx.conflict('Resource already exists');
      }

      // Generic server error
      return ctx.internalServerError('An unexpected error occurred');
    }
  },
}));
```

### 3. Service and Lifecycle Error Handling

**Use Strapi error classes** for deeper layers (services, lifecycles, policies).

```typescript
import { errors } from '@strapi/utils';
const { ApplicationError, ValidationError, NotFoundError, ForbiddenError } = errors;

// Service example
export default factories.createCoreService('api::entity.entity', ({ strapi }) => ({
  async customMethod(params) {
    try {
      // Validation
      if (!params.required) {
        throw new ValidationError('Required parameter missing', {
          field: 'required',
        });
      }

      // Business logic validation
      const existing = await strapi.entityService.findOne('api::entity.entity', params.id);
      if (!existing) {
        throw new NotFoundError('Entity not found', { id: params.id });
      }

      // Custom business rule
      if (!this.isValidOperation(existing, params)) {
        throw new ApplicationError('Operation not allowed', {
          reason: 'business_rule_violation',
          entity: existing.id,
        });
      }

      const result = await super.update(params.id, { data: params.data });
      return result;
    } catch (error) {
      strapi.log.error('Error in customMethod:', error);
      throw error; // Re-throw to let controller handle
    }
  },
}));

// Lifecycle example
export default {
  beforeCreate(event) {
    const { data } = event.params;

    // Validation that prevents creation
    if (!data.required_field) {
      throw new ApplicationError('Required field missing', {
        field: 'required_field',
      });
    }

    // Business rule validation
    if (data.price < 0) {
      throw new ApplicationError('Price cannot be negative', {
        price: data.price,
      });
    }
  },

  beforeUpdate(event) {
    const { data, where } = event.params;

    // Prevent certain updates
    if (data.status === 'locked') {
      throw new ForbiddenError('Cannot modify locked entity', {
        entityId: where.id,
      });
    }
  },
};
```

### 4. Policy Error Handling

**Use PolicyError class** for route policies with proper error details.

```typescript
import { errors } from '@strapi/utils';
const { PolicyError } = errors;

export default (policyContext, config, { strapi }) => {
  const { user } = policyContext.state;

  // Check if user is allowed
  const isAllowed = checkUserPermissions(user, policyContext);

  if (isAllowed) {
    return true;
  } else {
    throw new PolicyError('You are not allowed to perform this action', {
      policy: 'my-policy',
      userId: user?.id,
      action: policyContext.request.method,
      resource: policyContext.request.url,
    });
  }
};
```

### 5. Default Error Classes Reference

All error classes are available from `@strapi/utils` package:

```typescript
import { errors } from '@strapi/utils';
const {
  ApplicationError, // Generic application errors
  ValidationError, // Data validation errors
  NotFoundError, // 404 errors
  ForbiddenError, // 403 errors (authentication issues)
  UnauthorizedError, // 401 errors (permission issues)
  NotImplementedError, // 501 errors (feature not implemented)
  PayloadTooLargeError, // 413 errors (file/request too large)
  PaginationError, // Pagination-specific errors
  PolicyError, // Policy-specific errors
} = errors;

// ApplicationError - Most commonly used
throw new ApplicationError('Something went wrong', {
  code: 'CUSTOM_ERROR',
  details: 'Additional context',
});

// ValidationError - For data validation
throw new ValidationError('Invalid data provided', {
  field: 'email',
  value: 'invalid-email',
});

// NotFoundError - For missing resources
throw new NotFoundError('Entity not found', {
  entityId: 123,
});

// ForbiddenError - For authentication issues
throw new ForbiddenError('Invalid credentials', {
  attemptedAction: 'login',
});

// UnauthorizedError - For permission issues
throw new UnauthorizedError('Insufficient permissions', {
  requiredRole: 'admin',
  userRole: 'user',
});

// NotImplementedError - For unimplemented features
throw new NotImplementedError('Feature not available', {
  feature: 'advanced-search',
  plannedRelease: 'v2.0',
});

// PayloadTooLargeError - For size limits
throw new PayloadTooLargeError('File too large', {
  maxSize: '10MB',
  receivedSize: '15MB',
});

// PaginationError - For pagination issues
throw new PaginationError('Invalid page size', {
  maxPageSize: 100,
  requestedPageSize: 500,
});
```

### 6. Error Handling Best Practices

1. **Use appropriate error types**: Choose the most specific error class for the situation
2. **Provide helpful details**: Include context that helps debugging and user understanding
3. **Log appropriately**: Use `strapi.log.error()` for server errors, `console.error()` for service debugging
4. **Handle errors at the right level**: Controllers handle HTTP responses, services throw errors
5. **Validate early**: Check inputs and permissions before expensive operations
6. **Be consistent**: Use the same error patterns across your application

```typescript
// Good: Specific error with helpful details
if (!user.email) {
  throw new ValidationError('Email is required', {
    field: 'email',
    received: user.email,
    requirements: 'Valid email address',
  });
}

// Bad: Generic error without context
if (!user.email) {
  throw new Error('Invalid user');
}

// Good: Proper error handling in controller
try {
  const result = await service.processData(data);
  return { data: result };
} catch (error) {
  strapi.log.error('Data processing failed:', error);

  if (error instanceof ValidationError) {
    return ctx.badRequest(error.message, error.details);
  }

  if (error instanceof NotFoundError) {
    return ctx.notFound(error.message, error.details);
  }

  return ctx.internalServerError('Processing failed');
}
```

## Response Format Standards

### 1. Success Responses

```typescript
// Single entity
return { data: entity };

// Multiple entities
return {
  data: entities,
  meta: { pagination: { page: 1, pageSize: 25, total: 100 } },
};

// Custom responses
return {
  success: true,
  message: 'Operation completed',
  data: result,
};
```

### 2. Error Responses

```typescript
// Use Strapi's built-in error methods
return ctx.badRequest('Error message');
return ctx.unauthorized('Authentication required');
return ctx.notFound('Resource not found');
return ctx.forbidden('Access denied');
```

## Database Query Patterns

### 1. Document Service API (Strapi v5 - Recommended)

The Document Service API is the new recommended way to interact with content in Strapi v5. It replaces the Entity Service API and provides better support for Draft & Publish and Internationalization features.

**Key Concepts:**

- A **document** represents all variations of content for a given entry (draft/published, different locales)
- Document Service API returns **draft** versions by default
- REST/GraphQL APIs return **published** versions by default

```typescript
// Find multiple documents
const documents = await strapi.documents('api::{entity}.{entity}').findMany({
  filters: { field: value },
  populate: ['relation1', 'relation2'],
  sort: 'createdAt:desc',
  pagination: { page: 1, pageSize: 25 },
  status: 'published', // 'draft' (default) or 'published'
  locale: 'en', // specific locale or default
});

// Find single document by documentId
const document = await strapi.documents('api::{entity}.{entity}').findOne({
  documentId: 'a1b2c3d4e5f6g7h8i9j0klm',
  populate: ['relation1', 'relation2'],
  status: 'published',
  locale: 'en',
});

// Find first document matching criteria
const document = await strapi.documents('api::{entity}.{entity}').findFirst({
  filters: { field: value },
  populate: ['relation1'],
  status: 'published',
});

// Create document (creates draft by default)
const document = await strapi.documents('api::{entity}.{entity}').create({
  data: { field: value },
  status: 'published', // Optional: auto-publish on creation
  locale: 'en',
});

// Update document
const document = await strapi.documents('api::{entity}.{entity}').update({
  documentId: 'a1b2c3d4e5f6g7h8i9j0klm',
  data: { field: newValue },
  status: 'published', // Optional: auto-publish on update
  locale: 'en',
});

// Publish document (Draft & Publish feature)
await strapi.documents('api::{entity}.{entity}').publish({
  documentId: 'a1b2c3d4e5f6g7h8i9j0klm',
  locale: 'en', // or '*' for all locales
});

// Unpublish document
await strapi.documents('api::{entity}.{entity}').unpublish({
  documentId: 'a1b2c3d4e5f6g7h8i9j0klm',
  locale: 'en',
});

// Delete document
await strapi.documents('api::{entity}.{entity}').delete({
  documentId: 'a1b2c3d4e5f6g7h8i9j0klm',
  locale: 'en', // or '*' for all locales, or null for default locale only
});

// Count documents
const count = await strapi.documents('api::{entity}.{entity}').count({
  filters: { field: value },
  status: 'published',
  locale: 'en',
});
```

### 2. Entity Service (Legacy - Strapi v4 compatibility)

⚠️ **Deprecated in Strapi v5** - Use Document Service API instead

```typescript
// Find with relations (legacy)
const entities = await strapi.entityService.findMany('api::{entity}.{entity}', {
  filters: { field: value },
  populate: ['relation1', 'relation2'],
  sort: 'createdAt:desc',
  pagination: { page: 1, pageSize: 25 },
});

// Create (legacy)
const entity = await strapi.entityService.create('api::{entity}.{entity}', {
  data: { field: value },
});

// Update (legacy)
const entity = await strapi.entityService.update('api::{entity}.{entity}', id, {
  data: { field: newValue },
});
```

### 3. Relation Filtering (Common Pattern)

**IMPORTANT**: When filtering by relations in Strapi, always use the proper relation filter syntax:

```typescript
// ✅ CORRECT - Filter by relation ID
const userPrompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
  filters: {
    users_permissions_user: { id: userId }, // Wrap ID in object
  },
});

// ❌ WRONG - Direct ID will cause TypeScript errors
const userPrompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
  filters: {
    users_permissions_user: userId, // TypeScript error: Type 'number' has no properties in common
  },
});

// ✅ CORRECT - Sort using object syntax
const prompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
  sort: { updatedAt: 'desc' }, // Object syntax
});

// ❌ WRONG - String syntax may cause type errors
const prompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
  sort: 'updatedAt:desc', // String syntax (deprecated)
});
```

**Common Relation Filter Patterns**:

```typescript
// Filter by single relation ID
filters: { user: { id: userId } }

// Filter by multiple relation IDs
filters: { user: { id: { $in: [userId1, userId2] } } }

// Filter by relation field
filters: { user: { email: userEmail } }

// Complex relation filtering
filters: {
  user: {
    $and: [
      { id: userId },
      { confirmed: true }
    ]
  }
}
```

**Bidirectional Relationship Setup**:

When creating relations between content types, ensure both sides of the relationship are properly defined:

```json
// In user-prompt schema.json
{
  "users_permissions_user": {
    "type": "relation",
    "relation": "manyToOne",
    "target": "plugin::users-permissions.user",
    "inversedBy": "user_prompts"  // Must match the field name in user schema
  }
}

// In user schema.json (extensions/users-permissions/content-types/user/schema.json)
{
  "user_prompts": {
    "type": "relation",
    "relation": "oneToMany",
    "target": "api::user-prompt.user-prompt",
    "mappedBy": "users_permissions_user"  // Must match the field name in user-prompt schema
  }
}
```

**Common Relation Setup Errors**:
- Missing `inversedBy` field on the target model
- Mismatched field names between `inversedBy` and actual field name
- Forgetting to regenerate types after adding relations: `npm run strapi ts:generate-types`

### 4. Database Query (For Complex Queries)

```typescript
// Raw queries for complex operations
const results = await strapi.db.query('api::{entity}.{entity}').findMany({
  where: { field: value },
  select: ['field1', 'field2'],
  orderBy: { createdAt: 'desc' },
});
```

### 4. Document Service API Best Practices

```typescript
// Always specify status when querying published content
const publishedDocuments = await strapi.documents('api::{entity}.{entity}').findMany({
  status: 'published',
  filters: { active: true },
});

// Use documentId for updates, not the legacy id
const updatedDocument = await strapi.documents('api::{entity}.{entity}').update({
  documentId: document.documentId, // Use documentId, not id
  data: { field: newValue },
});

// Bulk operations with proper error handling
const batchSize = 50;
const documents = await strapi.documents('api::{entity}.{entity}').findMany({
  status: 'published',
  pagination: { limit: -1 }, // Get all records
});

for (let i = 0; i < documents.length; i += batchSize) {
  const batch = documents.slice(i, i + batchSize);

  const batchPromises = batch.map(async (doc) => {
    try {
      await strapi.documents('api::{entity}.{entity}').update({
        documentId: doc.documentId,
        data: { processed: true },
      });

      // Republish if needed
      await strapi.documents('api::{entity}.{entity}').publish({
        documentId: doc.documentId,
      });
    } catch (error) {
      console.error(`Error processing document ${doc.documentId}:`, error);
    }
  });

  await Promise.all(batchPromises);
}
```

### 5. Draft & Publish Migration (Strapi v4 → v5)

**⚠️ Breaking Change**: `publicationState` has been removed in Strapi v5 and replaced with `status`.

**Strapi v4 (Legacy)**:

```typescript
// OLD - Don't use in Strapi v5
const entities = await strapi.entityService.findMany('api::entity.entity', {
  publicationState: 'live', // Returns only published entries
  populate: {
    relation: {
      publicationState: 'live', // For populated relations
    },
  },
});
```

**Strapi v5 (Current)**:

```typescript
// NEW - Use Document Service API (Recommended)
const documents = await strapi.documents('api::entity.entity').findMany({
  status: 'published', // Returns published version
  populate: {
    relation: {
      status: 'published', // For populated relations
    },
  },
});

// For Entity Service (legacy compatibility)
const entities = await strapi.entityService.findMany('api::entity.entity', {
  filters: {
    publishedAt: { $ne: null }, // Filter for published content
  },
  populate: {
    relation: {
      filters: {
        publishedAt: { $ne: null }, // Filter populated relations
      },
    },
  },
});
```

**Migration Rules**:

- Replace `publicationState: 'live'` with `status: 'published'` (Document Service)
- Replace `publicationState: 'preview'` with `status: 'draft'` (or omit for both)
- For Entity Service, use `publishedAt: { $ne: null }` filter instead
- Update all populate queries to use the new syntax
- Consider migrating to Document Service API for new code

## Lifecycle Hooks and Webhooks

### Lifecycle Hooks (Strapi v5)

**Important Note**: In Strapi v5, lifecycle hooks are **deprecated** for most use cases. The recommended approach is to use **Document Service Middleware** instead.

**When to Still Use Lifecycle Hooks**:

- Users-permissions plugin operations
- Upload package operations
- Legacy compatibility requirements

**Location**: `src/api/{entity}/content-types/{entity}/lifecycles.ts`

**Available Lifecycle Events**:

- `beforeCreate` - Before entity creation
- `afterCreate` - After entity creation
- `beforeUpdate` - Before entity update
- `afterUpdate` - After entity update
- `beforeDelete` - Before entity deletion
- `afterDelete` - After entity deletion
- `beforeFindMany` - Before finding multiple entities
- `afterFindMany` - After finding multiple entities

**Rules**:

- Use for data validation and transformation
- Implement slug generation
- Handle related entity updates
- Use for statistics updates
- Always handle errors gracefully
- Prefer Document Service Middleware for new implementations

**Template**:

```typescript
export default {
  async beforeCreate(event) {
    const { data } = event.params;
    // Validation and transformation
    event.params.data = data;
  },

  async afterCreate(event) {
    const { result } = event;
    // Post-creation logic
  },

  async beforeUpdate(event) {
    const { data, where } = event.params;
    // Update validation
    event.params.data = data;
  },

  async afterUpdate(event) {
    const { result } = event;
    // Post-update logic
  },

  async beforeFindMany(event) {
    const { params } = event;
    // Modify query parameters before execution
    // Example: Add filters, modify sorting
    if (params.sort) {
      // Handle custom sorting logic
    }
  },

  async afterFindMany(event) {
    const { result, params } = event;
    // Post-process results
    // Example: Track search keywords, modify response
  },
};
```

### Webhooks

**Configuration Location**: `config/server.ts`

**Available Webhook Events**:

- `entry.create` - When a content entry is created
- `entry.update` - When a content entry is updated
- `entry.delete` - When a content entry is deleted
- `entry.publish` - When a content entry is published (Draft & Publish enabled)
- `entry.unpublish` - When a content entry is unpublished (Draft & Publish enabled)
- `media.create` - When media is uploaded
- `media.update` - When media is updated
- `media.delete` - When media is deleted

**Webhook Configuration**:

```typescript
// config/server.ts
export default {
  webhooks: {
    defaultHeaders: {
      Authorization: `Bearer ${process.env.WEBHOOK_TOKEN}`,
      'Custom-Header': 'my-custom-header',
    },
  },
};
```

**Webhook Payload Structure**:

```typescript
// Example entry.create payload
{
  "event": "entry.create",
  "createdAt": "2020-01-10T08:47:36.649Z",
  "model": "affiliate",
  "entry": {
    "id": 1,
    "name": "Example Affiliate",
    "url": "https://example.com",
    "createdAt": "2020-01-10T08:47:36.264Z",
    "updatedAt": "2020-01-10T08:47:36.264Z",
    // ... other fields
  }
}
```

**Webhook Security Best Practices**:

- Use HTTPS endpoints for webhook receivers
- Implement signature verification
- Use authentication tokens in headers
- Validate incoming request headers
- Implement rate limiting on webhook endpoints
- Log webhook events for monitoring

**Webhook vs Lifecycle Hooks**:

- **Webhooks**: External notifications to third-party services
- **Lifecycle Hooks**: Internal data processing and validation
- **Use Webhooks for**: CI/CD triggers, external system notifications, analytics
- **Use Lifecycle Hooks for**: Data validation, slug generation, internal statistics

### Document Service Middleware (Recommended for Strapi v5)

**Location**: `src/api/{entity}/middlewares/document-service.ts`

**Preferred over lifecycle hooks for new implementations**:

```typescript
// Example Document Service Middleware
export default (config, { strapi }) => {
  return {
    async beforeCreate(event) {
      const { data } = event.params;
      // Validation and transformation
      return { data: transformedData };
    },

    async afterCreate(event) {
      const { result } = event;
      // Post-creation logic
    },
  };
};
```

## Environment Configuration

### 1. Environment Variables

- Use `process.env.VARIABLE_NAME` for external services
- Store sensitive data in environment variables
- Use global configuration for application settings
- Follow naming convention: `SERVICE_SETTING_NAME`

### 2. Global Configuration

```typescript
// Use utility function for global config
import { getGlobalConfigProperty } from '../../../utils/global-config';

const setting = await getGlobalConfigProperty('setting_name', defaultValue);
```

## Middleware Standards

### 1. Global Middleware

**Location**: `src/middlewares/{middleware-name}.ts`

```typescript
export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Pre-processing logic

    await next();

    // Post-processing logic
  };
};
```

### 2. Register in Configuration

```typescript
// config/middlewares.ts
export default [
  'strapi::errors',
  'strapi::security',
  'strapi::cors',
  'global::{middleware-name}', // Custom middleware
];
```

## Cron Jobs

**Location**: `config/cron-tasks.ts`

**Rules**:

- Include comprehensive error handling
- Use proper logging
- Implement batch processing for large datasets
- Include environment checks (disable in development if needed)
- Use meaningful task names and descriptions

**Template**:

```typescript
export default {
  taskName: {
    task: async ({ strapi }) => {
      try {
        strapi.log.info('Starting scheduled task');

        // Task logic here

        strapi.log.info('Completed scheduled task');
      } catch (error) {
        strapi.log.error('Error in scheduled task:', error);
      }
    },
    options: {
      rule: '0 0 * * *', // Cron expression
    },
  },
};
```

## Testing Guidelines

### 1. Service Testing

- Test business logic in services
- Mock external dependencies
- Test error scenarios
- Validate data transformations

### 2. Controller Testing

- Test authentication flows
- Validate request/response formats
- Test error handling
- Verify authorization logic

## Security Best Practices

1. **Authentication**: Always verify `ctx.state.user` in protected endpoints
2. **Authorization**: Check user permissions for resource access
3. **Input Validation**: Validate all input parameters
4. **SQL Injection**: Use parameterized queries
5. **Rate Limiting**: Implement for public endpoints
6. **CORS**: Configure properly for frontend domains

## Performance Guidelines

1. **Database Queries**: Use selective population and pagination
2. **Batch Processing**: Process large datasets in chunks
3. **Caching**: Implement for frequently accessed data
4. **File Uploads**: Use streaming for large files
5. **Memory Management**: Clean up temporary files and resources

## Code Style

1. **TypeScript**: Use strict typing throughout
2. **Naming**: Use camelCase for variables, PascalCase for interfaces
3. **Comments**: Document complex business logic
4. **Formatting**: Use Prettier for consistent formatting
5. **Linting**: Follow ESLint rules

## Deployment

1. **Environment Variables**: Set all required variables
2. **Database Migrations**: Run before deployment
3. **Build Process**: Use `yarn build` for production
4. **Health Checks**: Implement `/health` endpoint
5. **Logging**: Use structured logging for production

## Specific Project Patterns

### 1. Referral System Implementation

**Key Components**:

- Referrer entity with balance tracking
- Referral entity linking referrer to referred user
- Referral Commission for earnings tracking
- Payout system for withdrawals

**Lifecycle Hook Patterns**:

```typescript
// Update referrer statistics after referral creation
async afterCreate(event) {
  const { result } = event;
  if (result.referrer) {
    await strapi.service('api::referrer.referrer').updateStatistics(result.referrer.id);
  }
}

// Handle balance updates in payout lifecycle
async afterUpdate(event) {
  const { result } = event;
  if (result.payout_status === 'completed') {
    // Deduct from referrer balance
    await updateReferrerBalance(result.referrer.id, -result.amount);
  }
}
```

### 2. AI Integration Patterns

**Service Structure**:

```typescript
// AI service calls with session management
const session = await strapi.entityService.create('api::aiscript-session.aiscript-session', {
  data: {
    session_id: `summary-${entity.slug}-${new Date().toISOString()}`,
    session_status: 'active',
    users_permissions_user: userId,
  },
});

const aiService = strapi.service('api::aiscript.aiscript');
const result = await aiService.processUserScript({
  message: promptContent,
  session,
  isUseDefaultPrompt: false,
});
```

### 3. External API Integration

**Client Pattern**:

```typescript
// utils/request.ts - Standardized API clients
export const ExternalAPIClient = {
  client: new API({
    baseURL: process.env.API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${process.env.API_KEY}`,
    },
  }),

  async getData(params) {
    try {
      const response = await this.client.get('/endpoint', { params });
      return response.data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  },
};
```

### 4. File Upload and S3 Integration

**S3 Upload Service Pattern**:

```typescript
// utils/s3-upload.ts
class S3UploadService {
  async uploadFile(filePath: string, fileName: string, folder?: string) {
    const fileContent = await fs.readFile(filePath);
    const contentType = mime.lookup(filePath) || 'application/octet-stream';

    const uploadCommand = new PutObjectCommand({
      Bucket: this.config.bucket,
      Key: `${this.config.rootPath}/${folder}/${fileName}`,
      Body: fileContent,
      ContentType: contentType,
      ACL: 'public-read',
    });

    const result = await this.s3Client.send(uploadCommand);
    return {
      success: true,
      url: `https://${this.config.bucket}.s3.amazonaws.com/${fileKey}`,
      key: fileKey,
    };
  }
}
```

### 5. User Tracking and Rate Limiting

**Middleware Pattern**:

```typescript
// middlewares/user-tracking.ts
export default (config, { strapi }) => {
  return async (ctx, next) => {
    // Extract and authenticate user
    const token = extractToken(ctx);
    if (token) {
      const user = await authenticateUser(token, strapi);
      if (user) ctx.state.user = user;
    }

    // Check rate limits for tracked endpoints
    if (shouldTrackRequest(ctx, config)) {
      if (ctx.state.user) {
        await checkUserLimits(ctx, strapi);
      } else {
        throw new UnauthorizedError('Authentication required');
      }
    }

    await next();

    // Track successful requests
    if (ctx.status === 200 && shouldTrackRequest(ctx, config)) {
      await trackRequest(ctx, strapi);
    }
  };
};
```

### 6. Slug Generation Pattern

**Lifecycle Implementation**:

```typescript
function generateSlug(name: string): string {
  if (!name) return '';
  return name
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');
}

async function validateSlug(data) {
  if (data.name && !data.slug) {
    data.slug = generateSlug(data.name);
  }
  return data;
}

export default {
  beforeCreate: async (event) => {
    event.params.data = await validateSlug(event.params.data);
  },
  beforeUpdate: async (event) => {
    event.params.data = await validateSlug(event.params.data);
  },
};
```

### 7. Batch Processing Pattern

**Cron Job Implementation**:

```typescript
// Process records in batches to avoid memory issues
const batchSize = 5;
const totalRecords = await strapi.entityService.count('api::entity.entity', {
  filters: { processed: false },
});

for (let offset = 0; offset < totalRecords; offset += batchSize) {
  const batch = await strapi.entityService.findMany('api::entity.entity', {
    filters: { processed: false },
    start: offset,
    limit: batchSize,
  });

  for (const record of batch) {
    try {
      await processRecord(record);
      await markAsProcessed(record.id);
    } catch (error) {
      strapi.log.error(`Error processing record ${record.id}:`, error);
    }
  }
}
```

### 8. Custom Route Patterns

**Authentication Required Routes**:

```typescript
export default {
  routes: [
    {
      method: 'GET',
      path: '/entities/user-specific',
      handler: 'entity.getUserSpecificData',
      config: {
        policies: [], // Add custom policies if needed
        middlewares: ['global::user-tracking'], // Apply tracking
      },
    },
    {
      method: 'POST',
      path: '/entities/process-payment',
      handler: 'entity.processPayment',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
```

## Common Utilities

### 1. Global Configuration Helper

```typescript
// utils/global-config.ts
export async function getGlobalConfigProperty<T>(
  propertyName: string,
  defaultValue?: T
): Promise<T> {
  try {
    const config = await getGlobalConfig();
    return config?.[propertyName] ?? defaultValue;
  } catch (error) {
    if (defaultValue !== undefined) return defaultValue;
    throw error;
  }
}
```

### 2. Error Classes

> **See comprehensive error handling documentation above** in the [Error Handling Standards](#error-handling-standards) section for complete error class reference and usage patterns.

```typescript
// Quick reference - import from @strapi/utils
import { errors } from '@strapi/utils';
const { ApplicationError, ValidationError, NotFoundError, ForbiddenError } = errors;

// Most common usage patterns
throw new ApplicationError('Custom error message', { code: 'CUSTOM_ERROR' });
throw new ValidationError('Validation failed', { field: 'email' });
throw new NotFoundError('Entity not found', { entityId: 123 });
```

### 3. Logging Standards

```typescript
// Use Strapi's logger
strapi.log.info('Operation started', { userId, operation: 'sync' });
strapi.log.error('Operation failed', error);
strapi.log.warn('Potential issue detected', { details });

// In services, use console for detailed debugging
console.log('Debug info:', { data });
console.error('Service error:', error);
```

## Package Management Rules

1. **Always use Yarn**: `yarn add package-name` or `yarn add -D package-name`
2. **Never edit package.json manually** for dependencies
3. **Update lock file**: Commit `yarn.lock` changes
4. **Environment-specific packages**: Use appropriate flags (`-D` for dev dependencies)

## Environment-Specific Configurations

### Development

- Enable detailed logging
- Disable external API calls where appropriate
- Use local database
- Enable hot reloading

### Production

- Minimize logging
- Enable all external integrations
- Use production database
- Enable caching and optimization

### UAT/Staging

- Mirror production settings
- Enable testing-specific features
- Use staging external services

This document should be updated as the project evolves and new patterns emerge.
