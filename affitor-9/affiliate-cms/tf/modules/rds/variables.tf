variable "environment" {
  description = "The environment name (e.g., dev, stage, prod)."
  type        = string
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "private_subnets" {
  description = "List of private subnet IDs"
  type        = list(string)
}

variable "public_subnets" {
  description = "List of public subnet IDs"
  type        = list(string)
}

variable "db_name" {
  description = "The name of the initial database."
  type        = string
}

variable "db_username" {
  description = "The username for the RDS master user."
  type        = string
}

variable "db_password" {
  description = "The password for the RDS master user."
  type        = string
  sensitive   = true
}

variable "engine_version" {
  description = "The engine version to use for the Aurora cluster.."
  type        = string
  default     = "16.6"  # Specify the PostgreSQL version you need
}

variable "instance_count" {
  description = "The number of instances to create in the Aurora cluster."
  type        = number
  default     = 1
}

variable "ecs_service_sg_id" {
  description = "The security group ID for the ECS service."
  type        = string
}

variable "serverless_min_capacity" {
  description = "The minimum capacity for Aurora Serverless v2 (in Aurora Capacity Units - ACU)"
  type        = number
  default     = 0.5  # Minimum capacity (0.5 ACU)
}

variable "serverless_max_capacity" {
  description = "The maximum capacity for Aurora Serverless v2 (in Aurora Capacity Units - ACU)"
  type        = number
  default     = 1.0  # Small maximum capacity (1 ACU)
}

variable "ip_address" {
  description = "IP address allowed to access RDS"
  type        = string
}
