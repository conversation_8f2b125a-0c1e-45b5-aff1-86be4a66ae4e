data "aws_caller_identity" "current" {}

# Cleanup existing role before creation
resource "null_resource" "role_cleanup" {
  provisioner "local-exec" {
    command = <<EOF
      # Detach policies
      aws iam list-attached-role-policies --role-name ${var.environment}-ecs-task-execution-role --query 'AttachedPolicies[*].PolicyArn' --output text | while read -r policy_arn; do
        aws iam detach-role-policy --role-name ${var.environment}-ecs-task-execution-role --policy-arn "$policy_arn" || true
      done

      # Delete inline policies
      aws iam list-role-policies --role-name ${var.environment}-ecs-task-execution-role --query 'PolicyNames[*]' --output text | while read -r policy_name; do
        aws iam delete-role-policy --role-name ${var.environment}-ecs-task-execution-role --policy-name "$policy_name" || true
      done

      # Delete role
      aws iam delete-role --role-name ${var.environment}-ecs-task-execution-role || true

      # Wait for deletion to propagate
      sleep 10
    EOF
  }
}

resource "aws_security_group" "ecs_service_sg" {
  name   = "${var.environment}-ecs-service-sg"
  vpc_id = var.vpc_id

  ingress {
    from_port       = 1337
    to_port         = 1337
    protocol        = "tcp"
    security_groups = [var.alb_security_group_id]  # Only allow traffic from ALB
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  depends_on = [var.target_group_arn]

  tags = {
    Environment = var.environment
  }
}

# Create ECS Task Execution Role
resource "aws_iam_role" "ecs_task_execution_role" {
  name = "${var.environment}-ecs-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })

  depends_on = [null_resource.role_cleanup]
}

# Attach AWS managed policy for ECS task execution
resource "aws_iam_role_policy_attachment" "ecs_task_execution_role_policy" {
  role       = aws_iam_role.ecs_task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Remove the problematic policy attachment and include the permissions in the custom policy
resource "aws_iam_role_policy" "ecs_task_execution_custom_policy" {
  name = "${var.environment}-ecs-task-execution-custom-policy"
  role = aws_iam_role.ecs_task_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams",
          "ecs:ExecuteCommand",
          "ecs:DescribeTasks",
          "ecs:ListTasks",
          "ecs:DescribeServices",
          "ecs:UpdateService",
          "elasticloadbalancing:*",
          "ec2:DescribeNetworkInterfaces",
          "ec2:CreateNetworkInterface",
          "ec2:DeleteNetworkInterface",
          "ec2:DescribeInstances",
          "ec2:AttachNetworkInterface"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:GetBucketLocation",
          "s3:ListBucket",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::affiliate-ecs-service-app-configuration",
          "arn:aws:s3:::affiliate-ecs-service-app-configuration/*",
          "arn:aws:s3:::strapi-affiliate",
          "arn:aws:s3:::strapi-affiliate/*"
        ]
      }
    ]
  })
}


resource "aws_ecs_task_definition" "this" {
  family                   = "ecs-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "1024"
  memory                   = "3072"
  execution_role_arn       = aws_iam_role.ecs_task_execution_role.arn

  container_definitions = jsonencode([
    {
      name      = "affiliate"
      image     = var.ecr_image
      cpu       = 0
      essential = true

      portMappings = [
        {
          name          = "port-fargate-1337"
          containerPort = 1337
          hostPort      = 1337
          protocol      = "tcp"
          appProtocol   = "http"
        }
      ]

      environment = []

      environmentFiles = [
        {
          value = "arn:aws:s3:::affiliate-ecs-service-app-configuration/${var.environment}.env"
          type  = "s3"
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/affiliate-${var.environment}-ecs-cluster-task"
          awslogs-region        = "us-east-1"
          awslogs-create-group  = "true"
          awslogs-stream-prefix = "ecs"
          mode                  = "non-blocking"
          max-buffer-size       = "25m"
        }
      }

    }
  ])

  runtime_platform {
    cpu_architecture       = "X86_64"
    operating_system_family = "LINUX"
  }
}

resource "aws_ecs_service" "this" {
  name            = "${var.environment}-ecs-service"
  cluster         = var.cluster
  task_definition = aws_ecs_task_definition.this.arn
  launch_type     = "FARGATE"
  desired_count   = 1
  force_new_deployment = true

  network_configuration {
    subnets         = var.subnets
    security_groups = [aws_security_group.ecs_service_sg.id]
  }
  load_balancer {
    target_group_arn = var.target_group_arn
    container_name   = "affiliate"
    container_port   = var.container_port
  }

  depends_on = [var.target_group_arn]
}
