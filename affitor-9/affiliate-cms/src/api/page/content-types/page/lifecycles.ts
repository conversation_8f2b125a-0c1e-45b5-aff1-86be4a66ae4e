/**
 * Page lifecycle hooks
 * Handles automatic referrer link generation when pages are published
 */

export default {
  /**
   * After updating a page, check if status changed to published and create referrer link
   */
  async afterUpdate(event) {
    const { data, where } = event.params;
    const { result } = event;

    // Add debug logging to see if lifecycle is triggered
    console.log('🔄 Page lifecycle afterUpdate triggered');
    console.log('   - Data:', JSON.stringify(data, null, 2));
    console.log('   - Where:', JSON.stringify(where, null, 2));
    console.log('   - Result:', JSON.stringify(result, null, 2));
    console.log('   - Has result:', !!result);
    strapi.log.info('Page lifecycle afterUpdate triggered');

    try {
      // Only proceed if page status was updated to 'published'
      if (data.status !== 'published') {
        console.log('❌ Page status not published, skipping referrer link creation. Status:', data.status);
        strapi.log.info('Page status not published, skipping referrer link creation');
        return;
      }

      console.log('✅ Page published, checking for referrer link creation');
      console.log('   - Page ID:', where.id);
      console.log('   - Document ID:', where.documentId);
      console.log('   - Status:', data.status);
      strapi.log.info('Page published, checking for referrer link creation');

      // Get the updated page with author information
      const page = await strapi.documents('api::page.page').findOne({
        documentId: result.documentId,
        populate: ['author']
      });

      if (!page || !page.author) {
        console.log('❌ Page or author not found for referrer link creation');
        console.log('   - Page found:', !!page);
        console.log('   - Author found:', !!page?.author);
        console.log('   - Page ID:', where.id);
        console.log('   - Document ID:', where.documentId);
        strapi.log.warn('Page or author not found for referrer link creation');
        return;
      }

      // Get the referrer for this user
      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: { user: { id: page.author.id } },
        fields: ['id', 'referral_code']
      });

      if (!referrers || referrers.length === 0) {
        console.log('❌ No referrer found for user, skipping referrer link creation');
        console.log('   - User ID:', page.author.id);
        console.log('   - Page ID:', page.id);
        strapi.log.info('No referrer found for user, skipping referrer link creation');
        return;
      }

      const referrer = referrers[0];
      console.log('✅ Found referrer for user');
      console.log('   - Referrer ID:', referrer.id);
      console.log('   - Referral Code:', referrer.referral_code);

      // Call the referrer link service to create the automatic link
      const referrerLinkService = strapi.service('api::referrer-link.referrer-link');
      console.log('🔗 Calling createAutomaticReferrerLink service...');

      const createdLink = await referrerLinkService.createAutomaticReferrerLink(page, referrer);

      console.log('✅ Automatic referrer link created for published page');
      console.log('   - Page ID:', page.id);
      console.log('   - Page Slug:', page.slug);
      console.log('   - Referrer ID:', referrer.id);
      console.log('   - Referral Code:', referrer.referral_code);
      console.log('   - Created Link ID:', createdLink?.id);
      strapi.log.info('Automatic referrer link created for published page');

    } catch (error) {
      console.log('❌ Error creating automatic referrer link for published page:', error.message);
      console.log('   - Stack:', error.stack);
      strapi.log.error('Error creating automatic referrer link for published page:', error);
      // Don't throw the error to avoid breaking the page update
    }
  }
};
