import * as admin from 'firebase-admin';

/**
 * Direct Firebase Email Service for authentication emails
 */
export default {
  // Initialize Firebase if it hasn't been initialized yet
  initializeFirebase() {
    if (!admin.apps.length) {
      // Initialize with service account credentials
      try {
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId: process.env.FIREBASE_PROJECT_ID,
            clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
            privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
          }),
        });
        strapi.log.info('Firebase initialized successfully');
      } catch (error) {
        strapi.log.error('Firebase initialization error:', error);
        throw new Error('Failed to initialize Firebase');
      }
    }
    return admin;
  },

  // Send email directly using Firebase
  async sendEmail(options) {
    const { to, subject, html } = options;

    try {
      this.initializeFirebase();

      // Use Firebase to send email (through Firestore triggered function)
      const result = await admin.firestore().collection('mail').add({
        to,
        message: {
          subject,
          html,
        },
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      strapi.log.info(`Email sent via Firebase: ${result.id}`);
      return { success: true, id: result.id };
    } catch (error) {
      strapi.log.error('Firebase email sending error:', error);
      throw new Error('Failed to send email via Firebase');
    }
  },

  // Send verification email
  async sendVerificationEmail(user, verificationToken) {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const verificationLink = `${frontendUrl}/auth/verify-email?token=${verificationToken}`;

    return this.sendEmail({
      to: user.email,
      subject: 'Confirm your email address',
      html: `
        <p>Hello ${user.fullname || user.username},</p>
        <p>Thank you for registering. Please click the link below to confirm your email:</p>
        <p><a href="${verificationLink}">Verify Email</a></p>
      `,
    });
  },

  // Send password reset email
  async sendPasswordResetEmail(user, resetToken) {
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const resetLink = `${frontendUrl}/auth/reset-password?token=${resetToken}`;

    return this.sendEmail({
      to: user.email,
      subject: 'Reset your password',
      html: `
        <p>Hello ${user.fullname || user.username},</p>
        <p>You requested to reset your password. Please click the link below to set a new password:</p>
        <p><a href="${resetLink}">Reset Password</a></p>
        <p>If you didn't request this, please ignore this email.</p>
      `,
    });
  },
};
