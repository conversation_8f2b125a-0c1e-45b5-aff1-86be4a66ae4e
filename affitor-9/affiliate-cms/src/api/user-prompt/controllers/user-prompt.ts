/**
 * user-prompt controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::user-prompt.user-prompt', ({ strapi }) => ({
  /**
   * Create a new user prompt
   */
  async create(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to create prompts');
      }

      const { title, content, description, tags, isFavorite } = ctx.request.body;

      // Validate required fields
      if (!title || !content) {
        return ctx.badRequest('Title and content are required');
      }

      // Validate field lengths
      if (title.length < 3 || title.length > 100) {
        return ctx.badRequest('Title must be between 3 and 100 characters');
      }

      if (content.length < 10 || content.length > 5000) {
        return ctx.badRequest('Content must be between 10 and 5000 characters');
      }

      if (description && description.length > 500) {
        return ctx.badRequest('Description must be less than 500 characters');
      }

      const promptData = {
        title,
        content,
        description: description || null,
        tags: Array.isArray(tags) ? tags : [],
        isFavorite: <PERSON><PERSON><PERSON>(isFavorite),
      };

      const prompt = await strapi.service('api::user-prompt.user-prompt').createUserPrompt(user.id, promptData);

      return {
        data: prompt,
        message: 'Prompt created successfully',
      };
    } catch (error) {
      console.error('Error in create user prompt controller:', error);
      return ctx.badRequest('Failed to create prompt', { error: error.message });
    }
  },

  /**
   * Get user prompts with search and filtering
   */
  async find(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to view prompts');
      }

      const {
        search,
        tags,
        isFavorite,
        limit = 25,
        start = 0,
        sortBy = 'updatedAt',
        sortOrder = 'desc'
      } = ctx.query;

      // Parse tags if provided as string
      let parsedTags;
      if (tags) {
        try {
          parsedTags = typeof tags === 'string' ? JSON.parse(tags) : tags;
        } catch (e) {
          parsedTags = Array.isArray(tags) ? tags : [tags];
        }
      }

      const options = {
        search,
        tags: parsedTags,
        isFavorite: isFavorite !== undefined ? Boolean(isFavorite) : undefined,
        limit: parseInt(String(limit)),
        start: parseInt(String(start)),
        sortBy,
        sortOrder,
      };

      const prompts = await strapi.service('api::user-prompt.user-prompt').getUserPrompts(user.id, options);

      return {
        data: prompts,
      };
    } catch (error) {
      console.error('Error in find user prompts controller:', error);
      return ctx.badRequest('Failed to fetch prompts', { error: error.message });
    }
  },

  /**
   * Get a single user prompt
   */
  async findOne(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to view prompts');
      }

      const { id } = ctx.params;
      const prompt = await strapi.service('api::user-prompt.user-prompt').getUserPromptById(user.id, id);

      if (!prompt) {
        return ctx.notFound('Prompt not found');
      }

      return {
        data: prompt,
      };
    } catch (error) {
      console.error('Error in findOne user prompt controller:', error);
      return ctx.badRequest('Failed to fetch prompt', { error: error.message });
    }
  },

  /**
   * Update a user prompt
   */
  async update(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to update prompts');
      }

      const { id } = ctx.params;
      const { title, content, description, tags, isFavorite } = ctx.request.body;

      // Validate field lengths if provided
      if (title && (title.length < 3 || title.length > 100)) {
        return ctx.badRequest('Title must be between 3 and 100 characters');
      }

      if (content && (content.length < 10 || content.length > 5000)) {
        return ctx.badRequest('Content must be between 10 and 5000 characters');
      }

      if (description && description.length > 500) {
        return ctx.badRequest('Description must be less than 500 characters');
      }

      const updateData: any = {};
      if (title !== undefined) updateData.title = title;
      if (content !== undefined) updateData.content = content;
      if (description !== undefined) updateData.description = description;
      if (tags !== undefined) updateData.tags = Array.isArray(tags) ? tags : [];
      if (isFavorite !== undefined) updateData.isFavorite = Boolean(isFavorite);

      const prompt = await strapi.service('api::user-prompt.user-prompt').updateUserPrompt(user.id, id, updateData);

      return {
        data: prompt,
        message: 'Prompt updated successfully',
      };
    } catch (error) {
      console.error('Error in update user prompt controller:', error);
      return ctx.badRequest('Failed to update prompt', { error: error.message });
    }
  },

  /**
   * Delete a user prompt
   */
  async delete(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to delete prompts');
      }

      const { id } = ctx.params;
      await strapi.service('api::user-prompt.user-prompt').deleteUserPrompt(user.id, id);

      return {
        message: 'Prompt deleted successfully',
      };
    } catch (error) {
      console.error('Error in delete user prompt controller:', error);
      return ctx.badRequest('Failed to delete prompt', { error: error.message });
    }
  },

  /**
   * Toggle favorite status
   */
  async toggleFavorite(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to favorite prompts');
      }

      const { id } = ctx.params;
      const prompt = await strapi.service('api::user-prompt.user-prompt').toggleFavorite(user.id, id);

      return {
        data: prompt,
        message: `Prompt ${prompt.isFavorite ? 'added to' : 'removed from'} favorites`,
      };
    } catch (error) {
      console.error('Error in toggle favorite controller:', error);
      return ctx.badRequest('Failed to toggle favorite', { error: error.message });
    }
  },

  /**
   * Increment usage count
   */
  async incrementUsage(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to use prompts');
      }

      const { id } = ctx.params;
      const prompt = await strapi.service('api::user-prompt.user-prompt').incrementUsage(user.id, id);

      return {
        data: prompt,
        message: 'Usage count updated',
      };
    } catch (error) {
      console.error('Error in increment usage controller:', error);
      return ctx.badRequest('Failed to update usage count', { error: error.message });
    }
  },

  /**
   * Get recent prompts
   */
  async getRecent(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to view recent prompts');
      }

      const prompts = await strapi.service('api::user-prompt.user-prompt').getRecentPrompts(user.id);

      return {
        data: prompts,
      };
    } catch (error) {
      console.error('Error in get recent prompts controller:', error);
      return ctx.badRequest('Failed to fetch recent prompts', { error: error.message });
    }
  },

  /**
   * Get user tags
   */
  async getTags(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to view tags');
      }

      const tags = await strapi.service('api::user-prompt.user-prompt').getUserTags(user.id);

      return {
        data: tags,
      };
    } catch (error) {
      console.error('Error in get user tags controller:', error);
      return ctx.badRequest('Failed to fetch tags', { error: error.message });
    }
  },
}));
