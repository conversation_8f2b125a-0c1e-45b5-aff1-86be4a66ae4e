{"kind": "collectionType", "collectionName": "social_listenings", "info": {"singularName": "social-listening", "pluralName": "social-listenings", "displayName": "Social Listening", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"type": {"type": "enumeration", "enum": ["video", "post"]}, "video_id": {"type": "string", "unique": true, "column": {"unique": true}}, "x_id": {"type": "string", "unique": true, "column": {"unique": true}}, "post_id": {"type": "string", "unique": true, "column": {"unique": true}}, "title": {"type": "text"}, "description": {"type": "text"}, "channel_title": {"type": "string"}, "channel_id": {"type": "string"}, "channel_avatar": {"type": "text"}, "published_from": {"type": "string"}, "thumbnail": {"type": "text"}, "duration": {"type": "string"}, "video_link": {"type": "string"}, "views": {"type": "biginteger"}, "likes": {"type": "integer"}, "shares": {"type": "integer"}, "comments": {"type": "integer"}, "link": {"type": "string"}, "keyword": {"type": "string"}, "platform": {"type": "enumeration", "enum": ["youtube", "tiktok", "reddit", "x"]}, "photos": {"type": "string"}, "affiliate": {"type": "relation", "relation": "oneToOne", "target": "api::affiliate.affiliate"}, "transcript": {"type": "text"}, "is_displayed": {"type": "boolean", "default": true}, "is_verified": {"type": "boolean", "default": false}, "is_from_crawler": {"type": "boolean", "default": false}}}