import { errors } from '@strapi/utils';
/**
 * Generate a slug from a string by lowercasing and replacing spaces with hyphens
 */
function generateSlug(name: string): string {
  if (!name) return '';
  return name
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');
}

/**
 * Validation functions for different aspects of affiliate data
 */

// Handle slug generation
async function validateSlug(data) {
  if (data.name) {
    data.slug = generateSlug(data.name);
  }
  return data;
}

/**
 * Main handler that composes all validation functions
 */
async function beforeSave(event) {
  let { data } = event.params;

  // Apply slug validation
  data = await validateSlug(data);

  event.params.data = data;
}

export default {
  beforeCreate: beforeSave,
  beforeUpdate: beforeSave,
};
