/**
 * referral-commission controller
 */

import { factories } from '@strapi/strapi';
import { logApiRequest, logQuery, logSuccess, logError } from '../../../utils/cloudwatch-logger';

export default factories.createCoreController(
  'api::referral-commission.referral-commission',
  ({ strapi }) => ({
    async find(ctx) {
      // Check authentication
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to access this resource');
      }

      logApiRequest('ReferralCommission', 'find', { userId: user.id });

      // First, find the referrer associated with this user
      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: {
          user: {
            id: user.id,
          },
        },
      });

      logSuccess('ReferralCommission', 'findReferrer', {
        userId: user.id,
        referrerCount: referrers?.length || 0,
      });

      if (!referrers || referrers.length === 0) {
        strapi.log.info(
          `[ReferralCommission] No referrer found for user ${user.id}, returning empty result`
        );
        return {
          data: [],
          meta: { pagination: { page: 1, pageSize: 25, pageCount: 0, total: 0 } },
        };
      }

      const referrerId = referrers[0].documentId;
      strapi.log.info(`[ReferralCommission] Using referrerId: ${referrerId} for user ${user.id}`);

      // Log query details using CloudWatch logger
      logQuery('ReferralCommission', {
        userId: user.id,
        operation: 'findCommissions',
        entityType: 'referral-commission',
        filters: ctx.query.filters || {},
        pagination: ctx.query.pagination,
      });

      try {
        // Use the service method with date validation
        const startTime = Date.now();
        const commissionService = strapi.service(
          'api::referral-commission.referral-commission'
        ) as any;
        const data = await commissionService.getCommissionsWithValidation(referrerId, ctx.query);
        const duration = Date.now() - startTime;

        // Create meta object similar to what super.find() would return
        const pagination = ctx.query.pagination as { page?: string; pageSize?: string } | undefined;
        const meta = {
          pagination: {
            page: parseInt(pagination?.page || '1'),
            pageSize: parseInt(pagination?.pageSize || '25'),
            pageCount: Math.ceil((data?.length || 0) / parseInt(pagination?.pageSize || '25')),
            total: data?.length || 0,
          },
        };

        logSuccess('ReferralCommission', 'findCommissions', {
          userId: user.id,
          resultCount: data?.length || 0,
          duration: `${duration}ms`,
        });

        return { data, meta };
      } catch (err) {
        logError('ReferralCommission', 'findCommissions', err, { userId: user.id });
        ctx.throw(500, err);
      }
    },

    async findOne(ctx) {
      // Check authentication
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to access this resource');
      }

      // First, find the referrer associated with this user
      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: { user: user.id },
      });

      if (!referrers || referrers.length === 0) {
        return ctx.notFound('No referrer found for this user');
      }

      const referrerId = referrers[0].documentId;

      try {
        // Get the commission with referrer check
        const commission: any = await strapi.entityService.findOne(
          'api::referral-commission.referral-commission',
          ctx.params.id,
          {
            populate: {
              referrer: true,
              referred_user: {
                fields: ['username', 'email'],
              },
              subscription_tier: {
                fields: ['name', 'display_name', 'price'],
              },
              referral: true,
            },
          }
        );

        if (!commission) {
          return ctx.notFound('Commission not found');
        }

        // Check if this commission belongs to the user's referrer
        const commissionReferrerId = commission.referrer?.documentId || commission.referrer?.id;
        if (commissionReferrerId !== referrerId) {
          return ctx.forbidden('You can only access your own commissions');
        }

        return this.transformResponse(commission);
      } catch (err) {
        strapi.log.error(
          `[ReferralCommission] FindOne failed for commission ${ctx.params.id}, user ${user.id}: ${err.message}`
        );
        ctx.throw(500, err);
      }
    },

    // Get commission statistics for the user
    async getStats(ctx) {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('You must be logged in to access this resource');
      }

      try {
        const commissionService = strapi.service(
          'api::referral-commission.referral-commission'
        ) as any;

        // First, find the referrer associated with this user
        const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
          filters: { user: user.id },
        });

        if (!referrers || referrers.length === 0) {
          return {
            totalCommissions: 0,
            pendingCommissions: 0,
            readyCommissions: 0,
            paidCommissions: 0,
            totalEarnings: 0,
            totalRevenue: 0,
            pendingEarnings: 0,
            readyEarnings: 0,
            paidEarnings: 0,
          };
        }

        const referrerDocId = referrers[0].documentId;

        // Use the commission service to get stats
        const stats = await commissionService.getCommissionStats(referrerDocId, true);

        strapi.log.info(
          `[ReferralCommission] Stats retrieved for user ${user.id}, referrer ${referrerDocId}: totalCommissions=${stats.totalCommissions}, totalEarnings=${stats.totalEarnings}`
        );

        return stats;
      } catch (err) {
        strapi.log.error(
          `[ReferralCommission] GetStats failed for user ${user.id}: ${err.message}`
        );
        ctx.throw(500, err);
      }
    },
  })
);
