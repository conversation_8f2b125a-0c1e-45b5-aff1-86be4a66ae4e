/**
 * Stripe webhook middleware
 * Handles raw body parsing for Stripe webhook requests
 */

export default (config, { strapi }) => {
  return async (ctx, next) => {
    console.log('Middleware: stripe-webhook');

    // Early capture of raw body before koa-body parses it
    // Only for Stripe webhook endpoint
    if (ctx.request.url.includes('/api/subscription-tiers/webhook')) {
      console.log('ctx.request.url', ctx.request.url);

      // Store a copy of the already parsed body if it exists
      if (ctx.request.body) {
        console.log('ctx.request.body', ctx.request.body);
        // Save the parsed body
        ctx._parsedBody = ctx.request.body;
      }

      // Check if we can access the raw request
      if (ctx.req.on) {
        console.log('ctx.req.on', ctx.req.on);

        // Convert the already parsed body back to a buffer
        // This is necessary because the koa body parser has already consumed the stream
        let bodyString = '';
        if (ctx._parsedBody) {
          bodyString = JSON.stringify(ctx._parsedBody);
        }

        // Use the string representation as our raw body
        ctx.request.rawBody = Buffer.from(bodyString);

        console.log('Raw body prepared from parsed body');
      } else {
        console.log('Cannot access raw request stream');
      }
    }

    // Continue with the next middleware
    await next();
  };
};
