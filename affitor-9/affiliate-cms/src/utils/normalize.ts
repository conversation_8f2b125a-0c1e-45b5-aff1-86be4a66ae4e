import _ from 'lodash';

/**
 * Normalizes user data for API responses
 * @param user - The user object to normalize
 * @returns A user object with only the specified fields
 */
export const normalizeUser = (user) => {
  return _.pick(user, [
    'id',
    'username',
    'email',
    'provider',
    'confirmed',
    'blocked',
    'role',
    'first_name',
    'last_name',
    'address',
    'apt',
    'city',
    'country',
    'zip_code',
    'state',
    'paypal_email',
    'bank_transfer',
    'user_tracking_request',
    'referrer',
    'createdAt',
    'updatedAt',
  ]);
};
